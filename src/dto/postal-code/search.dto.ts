import { ApiProperty } from "@nestjs/swagger";
import { IsString, <PERSON>NotEmpty, IsOptional } from "class-validator";

export class SearchPostalCodeDTO {

  @ApiProperty({
    description: "The input region - required",
    example: "Southern"
  })
  @IsString({ message: "Invalid region name!" })
  @IsNotEmpty({ message: "Region is required!" })
  region: string;

  @ApiProperty({
    description: "Input district",
    example: "Blantyre"
  })
  @IsOptional()
  @IsString({ message: "Invalid district name!" })
  district?: string;

  @ApiProperty({
    description: "Input location",
    example: "Chichiri"
  })
  @IsOptional()
  @IsString({ message: "Invalid location name!" })
  location?: string;

  @ApiProperty({
    description: "Input postal code",
    example: "312225"
  })
  @IsOptional()
  @IsString({ message: "Invalid postal code!" })
  postal_code?: string;
}
