import {
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  IsDateString,
  Length,
  Matches,
  IsNotEmpty,
} from 'class-validator';

export class CreateOrganizationDto {
  @IsString({ message: 'Organization name not valid!' })
  @Length(2, 255)
  @Matches(/^[a-zA-Z0-9\s\-]+$/, {
    message: 'Organization name must contain only letters, hyphens, numbers, and spaces',
  })
  @IsNotEmpty({ message: 'Organization name is required' })
  name: string;

  @IsString({ message: 'Registration number not valid!'})
  registration_number: string;

  @IsString()
  website: string;

  @IsEmail()
  email: string;

  @IsString()
  @Length(5, 20)
  phone: string;

  @IsOptional()
  @IsString()
  @Length(5, 20)
  fax?: string;

  @IsOptional()
  @IsUUID()
  physical_address_id?: string;

  @IsOptional()
  @IsUUID()
  postal_address_id?: string;

  @IsOptional()
  @IsUUID()
  contact_id?: string;

  @IsDateString()
  date_incorporation: Date;

  @IsString()
  place_incorporation: string;

  @IsOptional()
  @IsUUID()
  created_by?: string;
}
