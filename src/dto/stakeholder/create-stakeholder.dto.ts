import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otEmpt<PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  <PERSON>UUI<PERSON>,
  Length,
  MaxLength,
} from 'class-validator';


export class CreateStakeholderDto {
  @IsUUID('4',{ message: 'Application ID not valid!' })
  @IsNotEmpty({ message: 'Application ID is required' })
  application_id: string;

  @IsString({ message: 'First name contains invalid characters!' })
  @MaxLength(100, { message: 'First name must not exceed 100 characters' })
  @IsNotEmpty({ message: 'First name is required' })
  first_name: string;

  @IsString({ message: 'Last name contains invalid characters!' })
  @MaxLength(100, { message: 'Last name must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Last name is required' })
  last_name: string;

  @IsOptional()
  @IsString({ message: 'Middle name contains invalid characters!' })
  @Length(1, 100)
  middle_name?: string;

  @IsString({ message: 'Nationality contains invalid characters!' })
  @MaxLength(100, { message: 'Nationality must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Nationality is required' })
  nationality: string;

  @IsOptional()
  position: string;

  @IsString({ message: 'Profile contains invalid characters!' })
  @MaxLength(300, { message: 'Profile must not exceed 300 characters' })
  profile: string;

}
