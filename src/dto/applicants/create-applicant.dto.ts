import { IsString, IsEmail, IsOptional, IsUUID, IsDateString, Length, Matches } from 'class-validator';

export class CreateApplicantDto {
  @IsString()
  @Length(1, 255)
  name: string;

  @IsString()
  @Length(1, 255)
  business_registration_number: string;

  @IsString()
  @Length(1, 255)
  tpin: string;

  @IsString()
  @Length(1, 255)
  website: string;

  @IsEmail()
  @Length(1, 255)
  email: string;

  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @IsOptional()
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid fax number format' })
  fax?: string;

  @IsOptional()
  @IsString()
  level_of_insurance_cover?: string;

  @IsOptional()
  @IsUUID()
  address_id?: string;

  @IsOptional()
  @IsUUID()
  contact_id?: string;

  @IsDateString()
  date_incorporation: string;

  @IsString()
  @Length(1, 255)
  place_incorporation: string;
}
