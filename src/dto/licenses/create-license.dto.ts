import { IsString, <PERSON>E<PERSON>, <PERSON><PERSON>ptional, IsU<PERSON><PERSON>, IsDateString, Matches } from 'class-validator';
import { LicenseStatus } from '../../entities/licenses.entity';

export class CreateLicenseDto {
  @IsString()
  @Matches(/^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$/, { message: 'Invalid license number format' })
  license_number: string;

  @IsUUID()
  application_id: string;

  @IsUUID()
  applicant_id: string;

  @IsUUID()
  license_type_id: string;

  @IsOptional()
  @IsEnum(LicenseStatus)
  status?: LicenseStatus;

  @IsDateString()
  issue_date: string;

  @IsDateString()
  expiry_date: string;

  @IsUUID()
  issued_by: string;

  @IsOptional()
  @IsString()
  conditions?: string;
}
