import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  Max<PERSON>ength,
} from 'class-validator';

export class CreateProfessionalServicesDto {
  @IsUUID('4', { message: 'Application ID not valid!' })
  @IsNotEmpty({ message: 'Application ID is required' })
  application_id: string;

  @IsString({ message: 'Consultants contains invalid characters!' })
  @MaxLength(2000, { message: 'Consultants must not exceed 2000 characters' })
  @IsNotEmpty({ message: 'Consultants information is required' })
  consultants: string;

  @IsString({ message: 'Service providers contains invalid characters!' })
  @MaxLength(2000, { message: 'Service providers must not exceed 2000 characters' })
  @IsNotEmpty({ message: 'Service providers information is required' })
  service_providers: string;

  @IsString({ message: 'Technical support contains invalid characters!' })
  @MaxLength(2000, { message: 'Technical support must not exceed 2000 characters' })
  @IsNotEmpty({ message: 'Technical support information is required' })
  technical_support: string;

  @IsString({ message: 'Maintenance arrangements contains invalid characters!' })
  @MaxLength(2000, { message: 'Maintenance arrangements must not exceed 2000 characters' })
  @IsNotEmpty({ message: 'Maintenance arrangements information is required' })
  maintenance_arrangements: string;

  @IsString({ message: 'Professional partnerships contains invalid characters!' })
  @MaxLength(2000, { message: 'Professional partnerships must not exceed 2000 characters' })
  @IsOptional()
  professional_partnerships?: string;

  @IsString({ message: 'Outsourced services contains invalid characters!' })
  @MaxLength(2000, { message: 'Outsourced services must not exceed 2000 characters' })
  @IsOptional()
  outsourced_services?: string;

  @IsString({ message: 'Quality assurance contains invalid characters!' })
  @MaxLength(2000, { message: 'Quality assurance must not exceed 2000 characters' })
  @IsOptional()
  quality_assurance?: string;

  @IsString({ message: 'Training programs contains invalid characters!' })
  @MaxLength(2000, { message: 'Training programs must not exceed 2000 characters' })
  @IsOptional()
  training_programs?: string;
}
