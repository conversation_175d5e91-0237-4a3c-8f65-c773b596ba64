import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsEmail,
  Is<PERSON>rl,
  <PERSON>Length,
  <PERSON><PERSON>eng<PERSON>,
  IsPhoneNumber,
  IsJSON,
} from 'class-validator';
import { ClientSystemStatus, ClientSystemType } from '../../entities/client-systems.entity';

export class CreateClientSystemDto {
  @ApiProperty({
    description: 'Name of the client system',
    example: 'MACRA Mobile App',
    maxLength: 255,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Unique system code identifier',
    example: 'MACRA_MOBILE_V1',
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  system_code: string;

  @ApiProperty({
    description: 'Description of the client system',
    example: 'Mobile application for license applications and management',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Type of the client system',
    enum: ClientSystemType,
    example: ClientSystemType.MOBILE_APP,
  })
  @IsEnum(ClientSystemType)
  system_type: ClientSystemType;

  @ApiProperty({
    description: 'Status of the client system',
    enum: ClientSystemStatus,
    example: ClientSystemStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(ClientSystemStatus)
  status?: ClientSystemStatus;

  @ApiProperty({
    description: 'API endpoint URL for the client system',
    example: 'https://api.macra.gov.zm/mobile/v1',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  api_endpoint?: string;

  @ApiProperty({
    description: 'Callback URL for notifications and webhooks',
    example: 'https://mobile.macra.gov.zm/callback',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(500)
  callback_url?: string;

  @ApiProperty({
    description: 'Contact email for the system administrator',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  contact_email?: string;

  @ApiProperty({
    description: 'Contact phone number',
    example: '+260211123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  contact_phone?: string;

  @ApiProperty({
    description: 'Organization or department responsible for the system',
    example: 'MACRA IT Department',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  organization?: string;

  @ApiProperty({
    description: 'JSON string of access permissions',
    example: '{"read": true, "write": false, "admin": false}',
    required: false,
  })
  @IsOptional()
  @IsString()
  access_permissions?: string;

  @ApiProperty({
    description: 'Version of the client system',
    example: '1.0.0',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  version?: string;

  @ApiProperty({
    description: 'Additional notes about the system',
    example: 'Primary mobile application for customer portal',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
