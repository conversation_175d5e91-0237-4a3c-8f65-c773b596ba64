import { <PERSON>String, IsEnum, IsOptional, IsUUID, IsNumber, IsBoolean, IsArray, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EvaluationType, EvaluationStatus, EvaluationRecommendation } from '../../entities/evaluations.entity';

export class CreateEvaluationCriteriaDto {
  @ApiProperty({
    description: 'Evaluation category (e.g., financial_capacity, technical_expertise)',
    example: 'financial_capacity',
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: 'Evaluation subcategory (e.g., financial_documents, capital_adequacy)',
    example: 'financial_documents',
  })
  @IsString()
  subcategory: string;

  @ApiProperty({
    description: 'Score for this criterion (0-100)',
    example: 85.5,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  score: number;

  @ApiProperty({
    description: 'Weight of this criterion in overall score (0-1)',
    example: 0.2,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  weight: number;

  @ApiPropertyOptional({
    description: 'Maximum marks for this criterion',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  max_marks?: number;

  @ApiPropertyOptional({
    description: 'Awarded marks for this criterion',
    example: 8,
  })
  @IsOptional()
  @IsNumber()
  awarded_marks?: number;
}

export class CreateEvaluationDto {
  @ApiProperty({
    description: 'UUID of the application being evaluated',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  application_id: string;

  @ApiProperty({
    description: 'UUID of the evaluator (staff member)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  evaluator_id: string;

  @ApiProperty({
    description: 'Type of evaluation based on license category',
    enum: EvaluationType,
    example: EvaluationType.POSTAL_SERVICE,
  })
  @IsEnum(EvaluationType)
  evaluation_type: EvaluationType;

  @ApiPropertyOptional({
    description: 'Evaluation status',
    enum: EvaluationStatus,
    default: EvaluationStatus.DRAFT,
    example: EvaluationStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(EvaluationStatus)
  status?: EvaluationStatus;

  @ApiProperty({
    description: 'Total evaluation score (0-100)',
    example: 78.5,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  total_score: number;

  @ApiProperty({
    description: 'Evaluation recommendation',
    enum: EvaluationRecommendation,
    example: EvaluationRecommendation.APPROVE,
  })
  @IsEnum(EvaluationRecommendation)
  recommendation: EvaluationRecommendation;

  @ApiPropertyOptional({
    description: 'Evaluator notes and comments',
    example: 'Application meets all technical requirements. Financial projections are realistic.',
  })
  @IsOptional()
  @IsString()
  evaluators_notes?: string;

  @ApiPropertyOptional({
    description: 'Whether shareholding compliance requirements are met',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  shareholding_compliance?: boolean;

  @ApiPropertyOptional({
    description: 'Evaluation criteria scores',
    type: [CreateEvaluationCriteriaDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEvaluationCriteriaDto)
  criteria?: CreateEvaluationCriteriaDto[];
}
