import { IsString, <PERSON>U<PERSON><PERSON>, <PERSON>Optional, IsDateString, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskPriority } from '../../entities/tasks.entity';

export class AssignTaskDto {
  @ApiProperty({ description: 'User ID to assign task to' })
  @IsUUID()
  assignedTo: string;

  @ApiPropertyOptional({ description: 'Assignment comment or reason' })
  @IsString()
  @IsOptional()
  comment?: string;

  @ApiPropertyOptional({ description: 'Due date for the task (ISO 8601 format)' })
  @IsDateString()
  @IsOptional()
  due_date?: string;

  @ApiPropertyOptional({
    description: 'Task priority',
    enum: TaskPriority,
    example: TaskPriority.MEDIUM
  })
  @IsEnum(TaskPriority)
  @IsOptional()
  priority?: TaskPriority;

  @ApiPropertyOptional({ description: 'Additional assignment notes' })
  @IsString()
  @IsOptional()
  assignment_notes?: string;
}