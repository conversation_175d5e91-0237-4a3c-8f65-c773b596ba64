import { IsEmail, IsString, IsStrongPassword, IsUUID } from 'class-validator';

export class ForgotPasswordDto {
  @IsEmail()
  email: string;
}

export class ResetPasswordDto {
  @IsString()
  code: string;

  @IsUUID()
  user_id: string;

  @IsString()
  unique: string;

  @IsString()
  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 3,
    minSymbols: 1,
  })
  new_password: string;
}
