import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateAddressDto } from '../dto/address/create.dto';
import { UpdateAddressDto } from '../dto/address/update.dto';
import { Address } from '../entities/address.entity';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class AddressService {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Create a new address entry
   * @param createAddressDto Address DTO
   * @param createdBy User ID
   * @returns Address Object
   */
  async createAddress(
    createAddressDto: CreateAddressDto,
    createdBy: string,
  ): Promise<Address> {
    try {
      console.log('Creating address with data:', createAddressDto);
      console.log('Created by:', createdBy);

      const { entity_type, address_type, address_line_1 } = createAddressDto;

      return this.dataSource.transaction(async (manager) => {
        const repo = manager.getRepository(Address);

        // Check for existing address (optional - can be removed if duplicates are allowed)
        if (entity_type && address_type && address_line_1) {
          console.log('Checking for existing address...');
          const existing = await repo
            .createQueryBuilder('address')
            .where('address.entity_type = :entity_type', { entity_type })
            .andWhere('address.address_type = :address_type', { address_type })
            .andWhere('address.address_line_1 = :address_line_1', { address_line_1 })
            .andWhere('address.created_by = :created_by', { created_by: createdBy })
            .getOne();

          if (existing) {
            console.log('Existing address found:', existing);
            throw new ConflictException('Address already exists');
          }
          console.log('No existing address found, proceeding...');
        }

        console.log('Creating address entity...');
        const address = repo.create({
          ...createAddressDto,
          created_by: createdBy,
        });
        console.log('Address entity created:', address);

        try {
          console.log('Saving address to database...');
          const savedAddress = await repo.save(address);
          console.log('Address saved successfully:', savedAddress);
          return savedAddress;
        } catch (error) {
          console.error('Error saving address:', error);
          console.error('Error details:', {
            code: error.code,
            message: error.message,
            detail: error.detail,
            constraint: error.constraint
          });

          // Optional: stricter check if not already covered by DB constraint
          if (error.code === '23505') {
            throw new ConflictException('Duplicate address not allowed');
          }
          throw error;
        }
      });
    } catch (error) {
      console.error('Error in createAddress method:', error);
      throw error;
    }
  }

  /**
   * Edit an existing address
   * @param addressId Address ID from URL parameter
   * @param updateAddressDto Update Address DTO
   * @param updatedBy User ID
   * @returns Address Object
   */
  async editAddress(
    addressId: string,
    updateAddressDto: UpdateAddressDto,
    updatedBy: string,
  ): Promise<Address> {
    const address = await this.addressRepository.findOne({
      where: { address_id: addressId },
    });

    if (!address) {
      throw new NotFoundException('Address not found!');
    }

    Object.assign(address, updateAddressDto, { updated_by: updatedBy });

    return this.addressRepository.save(address);
  }

  /**
 * Get all addresses (optionally filtered by entity_type, entity_id, or address_type)
 */
  async findAll(filter?: { entity_type?: string; entity_id?: string; address_type?: string }): Promise<Address[]> {
    const query = this.addressRepository.createQueryBuilder('address');

    if (filter?.entity_type) {
      query.andWhere('address.entity_type = :entity_type', { entity_type: filter.entity_type });
    }

    if (filter?.entity_id) {
      query.andWhere('address.entity_id = :entity_id', { entity_id: filter.entity_id });
    }

    if (filter?.address_type) {
      query.andWhere('address.address_type = :address_type', { address_type: filter.address_type });
    }

    return query.getMany();
  }

  /**
   * Get a single address by ID
   */
  async findOneById(id: string): Promise<Address> {
    const address = await this.addressRepository.findOne({
      where: { address_id: id },
    });

    if (!address) {
      throw new NotFoundException(`Address with ID ${id} not found`);
    }

    return address;
  }

  /**
   * Soft delete an address by ID
   */
  async softDelete(id: string, deletedBy: string): Promise<void> {
    const address = await this.findOneById(id);

    address.updated_by = deletedBy;
    await this.addressRepository.save(address);
    await this.addressRepository.softDelete(id);
  }

  /**
   * Restore a soft-deleted address
   */
  async restore(id: string): Promise<void> {
    const address = await this.addressRepository
      .createQueryBuilder('address')
      .withDeleted()
      .where('address.address_id = :id', { id })
      .getOne();

    if (!address) {
      throw new NotFoundException(`Deleted address with ID ${id} not found`);
    }

    await this.addressRepository.restore(id);
  }

  /**
   * Permanently remove an address
   * Use with caution!
   */
  async hardDelete(id: string): Promise<void> {
    const address = await this.findOneById(id);
    await this.addressRepository.remove(address);
  }

}
