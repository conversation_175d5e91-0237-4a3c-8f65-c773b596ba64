import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationStatusTrackingController } from '../application-status-tracking.controller';
import { ApplicationStatusTrackingService } from '../../services/application-status-tracking.service';
import { ApplicationStatus } from '../../entities/applications.entity';
import { UpdateApplicationStatusDto } from '../../dto/application-status/update-application-status.dto';

describe('ApplicationStatusTrackingController', () => {
  let controller: ApplicationStatusTrackingController;
  let service: ApplicationStatusTrackingService;

  const mockApplicationStatusTrackingService = {
    updateApplicationStatus: jest.fn(),
    getApplicationStatusTracking: jest.fn(),
    getStatusHistory: jest.fn(),
    getApplicationsByStatus: jest.fn(),
  };

  const mockApplicationData = {
    application_id: '123e4567-e89b-12d3-a456-426614174000',
    application_number: 'APP-2024-001',
    current_status: ApplicationStatus.UNDER_REVIEW,
    current_step: 2,
    progress_percentage: 50,
    submitted_at: new Date('2024-01-10T10:00:00Z'),
    created_at: new Date('2024-01-10T09:00:00Z'),
    updated_at: new Date('2024-01-15T10:00:00Z'),
    status_history: [],
    applicant: {
      name: 'Test Company Ltd',
      email: '<EMAIL>',
      business_registration_number: 'BRN123456'
    },
    license_category: {
      name: 'Individual License A',
      description: 'License for individual operations'
    }
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicationStatusTrackingController],
      providers: [
        {
          provide: ApplicationStatusTrackingService,
          useValue: mockApplicationStatusTrackingService,
        },
      ],
    }).compile();

    controller = module.get<ApplicationStatusTrackingController>(ApplicationStatusTrackingController);
    service = module.get<ApplicationStatusTrackingService>(ApplicationStatusTrackingService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateApplicationStatus', () => {
    it('should update application status successfully', async () => {
      const applicationId = '123e4567-e89b-12d3-a456-426614174000';
      const updateStatusDto: UpdateApplicationStatusDto = {
        status: ApplicationStatus.UNDER_REVIEW,
        comments: 'Application moved to review',
        reason: 'All documents verified'
      };
      const mockRequest = {
        user: { user_id: 'user-123' }
      };

      mockApplicationStatusTrackingService.updateApplicationStatus.mockResolvedValue(mockApplicationData);

      const result = await controller.updateApplicationStatus(
        applicationId,
        updateStatusDto,
        mockRequest
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Application status updated successfully');
      expect(result.data).toEqual(mockApplicationData);
      expect(result.meta.application_id).toBe(applicationId);
      expect(result.meta.updated_by).toBe('user-123');
      expect(result.meta.new_status).toBe(ApplicationStatus.UNDER_REVIEW);
      expect(service.updateApplicationStatus).toHaveBeenCalledWith(
        applicationId,
        updateStatusDto,
        'user-123'
      );
    });
  });

  describe('getApplicationStatusTracking', () => {
    it('should get application status tracking successfully', async () => {
      const applicationId = '123e4567-e89b-12d3-a456-426614174000';

      mockApplicationStatusTrackingService.getApplicationStatusTracking.mockResolvedValue(mockApplicationData);

      const result = await controller.getApplicationStatusTracking(applicationId);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Application status tracking retrieved successfully');
      expect(result.data).toEqual(mockApplicationData);
      expect(result.meta.application_id).toBe(applicationId);
      expect(result.meta.current_status).toBe(ApplicationStatus.UNDER_REVIEW);
      expect(result.meta.progress_percentage).toBe(50);
      expect(service.getApplicationStatusTracking).toHaveBeenCalledWith(applicationId);
    });
  });

  describe('getApplicationStatusHistory', () => {
    it('should get application status history successfully', async () => {
      const applicationId = '123e4567-e89b-12d3-a456-426614174000';
      const mockHistory = [
        {
          history_id: 'hist-001',
          application_id: applicationId,
          status: ApplicationStatus.UNDER_REVIEW,
          previous_status: ApplicationStatus.SUBMITTED,
          changed_by_name: 'John Doe',
          changed_at: new Date(),
          comments: 'Moved to review',
          reason: 'Documents verified',
          estimated_completion_date: null
        }
      ];

      mockApplicationStatusTrackingService.getStatusHistory.mockResolvedValue(mockHistory);

      const result = await controller.getApplicationStatusHistory(applicationId);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Application status history retrieved successfully');
      expect(result.data).toEqual(mockHistory);
      expect(result.meta.application_id).toBe(applicationId);
      expect(result.meta.total_records).toBe(1);
      expect(result.meta.latest_status).toBe(ApplicationStatus.UNDER_REVIEW);
      expect(service.getStatusHistory).toHaveBeenCalledWith(applicationId);
    });
  });

  describe('getApplicationsByStatus', () => {
    it('should get applications by status successfully', async () => {
      const status = ApplicationStatus.UNDER_REVIEW;
      const mockApplications = [mockApplicationData];

      mockApplicationStatusTrackingService.getApplicationsByStatus.mockResolvedValue(mockApplications);

      const result = await controller.getApplicationsByStatus(status);

      expect(result.success).toBe(true);
      expect(result.message).toBe(`Applications with status '${status}' retrieved successfully`);
      expect(result.data).toEqual(mockApplications);
      expect(result.meta.status).toBe(status);
      expect(result.meta.total_applications).toBe(1);
      expect(result.meta.applications_found).toBe(true);
      expect(service.getApplicationsByStatus).toHaveBeenCalledWith(status);
    });
  });

  describe('getAvailableStatuses', () => {
    it('should get available statuses successfully', async () => {
      const result = await controller.getAvailableStatuses();

      expect(result.success).toBe(true);
      expect(result.message).toBe('Application statuses retrieved successfully');
      expect(result.data).toBeInstanceOf(Array);
      expect(result.data.length).toBe(5); // 5 statuses defined in enum
      expect(result.meta.total_statuses).toBe(5);
      
      // Check if all statuses are present
      const statusValues = result.data.map(status => status.value);
      expect(statusValues).toContain(ApplicationStatus.SUBMITTED);
      expect(statusValues).toContain(ApplicationStatus.UNDER_REVIEW);
      expect(statusValues).toContain(ApplicationStatus.EVALUATION);
      expect(statusValues).toContain(ApplicationStatus.APPROVED);
      expect(statusValues).toContain(ApplicationStatus.REJECTED);
    });
  });
});