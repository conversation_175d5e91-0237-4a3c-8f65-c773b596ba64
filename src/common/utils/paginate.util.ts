import { Repository, SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import {
  PaginateQuery,
  PaginateConfig,
  PaginatedResult,
} from '../interfaces/pagination.interface';

export async function paginate<T extends ObjectLiteral>(
  queryBuilder: SelectQueryBuilder<T>,
  config: PaginateConfig<T>,
  query: PaginateQuery,
  baseUrl: string = ''
): Promise<PaginatedResult<T>> {
  const page = Math.max(1, query.page || 1);
  const limit = Math.min(
    query.limit || config.defaultLimit || 10,
    config.maxLimit || 100
  );

  // Apply search
  if (query.search && config.searchableColumns && config.searchableColumns.length > 0) {
    const searchConditions = config.searchableColumns
      .map((column, index) => `${queryBuilder.alias}.${String(column)} ILIKE :search${index}`)
      .join(' OR ');
    
    queryBuilder.andWhere(`(${searchConditions})`, 
      config.searchableColumns.reduce((params, column, index) => {
        params[`search${index}`] = `%${query.search}%`;
        return params;
      }, {} as Record<string, any>)
    );
  }

  // Apply filters
  if (query.filter && config.filterableColumns) {
    Object.entries(query.filter).forEach(([key, value]) => {
      if (config.filterableColumns && key in config.filterableColumns) {
        if (Array.isArray(value)) {
          queryBuilder.andWhere(`${queryBuilder.alias}.${key} IN (:...${key})`, { [key]: value });
        } else {
          queryBuilder.andWhere(`${queryBuilder.alias}.${key} = :${key}`, { [key]: value });
        }
      }
    });
  }

  // Apply sorting
  let sortByPairs: [keyof T, 'ASC' | 'DESC'][] = [];

  if (query.sortBy && query.sortBy.length > 0) {
    // Parse sortBy strings like "column:ASC" or "column:DESC"
    sortByPairs = query.sortBy.map(sortStr => {
      const [column, direction = 'ASC'] = sortStr.split(':');
      return [column as keyof T, direction.toUpperCase() as 'ASC' | 'DESC'];
    });
  } else if (config.defaultSortBy) {
    sortByPairs = config.defaultSortBy;
  } else {
    sortByPairs = [['created_at' as keyof T, 'DESC']];
  }

  sortByPairs.forEach(([column, direction], index) => {
    if (config.sortableColumns.includes(column)) {
      if (index === 0) {
        queryBuilder.orderBy(`${queryBuilder.alias}.${String(column)}`, direction);
      } else {
        queryBuilder.addOrderBy(`${queryBuilder.alias}.${String(column)}`, direction);
      }
    }
  });

  // Apply pagination
  const skip = (page - 1) * limit;
  queryBuilder.skip(skip).take(limit);

  // Execute query
  const [data, totalItems] = await queryBuilder.getManyAndCount();

  const totalPages = Math.ceil(totalItems / limit);

  // Build links
  const buildUrl = (page: number) => {
    const params = new URLSearchParams();
    params.set('page', page.toString());
    params.set('limit', limit.toString());
    
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    
    Object.entries(query.filter || {}).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => params.append(`filter.${key}`, v));
      } else {
        params.set(`filter.${key}`, value);
      }
    });

    return `${baseUrl}?${params.toString()}`;
  };

  return {
    data,
    meta: {
      itemsPerPage: limit,
      totalItems,
      currentPage: page,
      totalPages,
      sortBy: sortByPairs.map(([column, direction]) => [String(column), direction]),
      searchBy: query.searchBy || [],
      search: query.search || '',
      filter: query.filter || {},
    },
    links: {
      first: buildUrl(1),
      previous: page > 1 ? buildUrl(page - 1) : '',
      current: buildUrl(page),
      next: page < totalPages ? buildUrl(page + 1) : '',
      last: buildUrl(totalPages),
    },
  };
}
