import { validate } from 'class-validator';
import { Applications, ApplicationStatus } from '../applications.entity';
import { Applicants } from '../applicant.entity';
import { LicenseCategories } from '../license-categories.entity';
import { User } from '../user.entity';

describe('Applications Entity', () => {
  let application: Applications;
  let mockApplicant: Applicants;
  let mockLicenseCategory: LicenseCategories;
  let mockUser: User;

  beforeEach(() => {
    mockApplicant = {
      applicant_id: 'applicant-123',
      name: 'Test Company Ltd',
      email: '<EMAIL>',
    } as Applicants;

    mockLicenseCategory = {
      license_category_id: 'category-123',
      name: 'Individual License A',
      fee: '1000.00',
    } as LicenseCategories;

    mockUser = {
      user_id: 'user-123',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
    } as User;

    application = new Applications();
    application.application_id = 'app-123';
    application.application_number = 'APP-2024-001';
    application.applicant_id = 'applicant-123';
    application.license_category_id = 'category-123';
    application.status = ApplicationStatus.SUBMITTED;
    application.current_step = 1;
    application.progress_percentage = 10;
    application.created_by = 'user-123';
    application.created_at = new Date();
    application.updated_at = new Date();
  });

  describe('Entity Creation', () => {
    it('should create an application with valid data', () => {
      expect(application).toBeDefined();
      expect(application.application_id).toBe('app-123');
      expect(application.application_number).toBe('APP-2024-001');
      expect(application.status).toBe(ApplicationStatus.SUBMITTED);
      expect(application.current_step).toBe(1);
      expect(application.progress_percentage).toBe(10);
    });

    it('should have correct default status', () => {
      const newApplication = new Applications();
      // Default status should be set by the entity decorator
      expect(ApplicationStatus.SUBMITTED).toBeDefined();
    });
  });

  describe('Validation', () => {
    it('should validate successfully with all required fields', async () => {
      const errors = await validate(application);
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid application number format', async () => {
      application.application_number = 'INVALID-FORMAT';
      const errors = await validate(application);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'application_number')).toBe(true);
    });

    it('should fail validation with invalid current step', async () => {
      application.current_step = 0; // Below minimum
      const errors = await validate(application);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'current_step')).toBe(true);
    });

    it('should fail validation with invalid progress percentage', async () => {
      application.progress_percentage = 150; // Above maximum
      const errors = await validate(application);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'progress_percentage')).toBe(true);
    });

    it('should validate valid application number formats', async () => {
      const validFormats = ['APP-2024-001', 'LIC-2024-123', 'TEL-2024-01'];
      
      for (const format of validFormats) {
        application.application_number = format;
        const errors = await validate(application);
        const applicationNumberErrors = errors.filter(error => error.property === 'application_number');
        expect(applicationNumberErrors.length).toBe(0);
      }
    });
  });

  describe('Status Management', () => {
    it('should handle all application statuses', () => {
      const statuses = Object.values(ApplicationStatus);
      expect(statuses).toContain(ApplicationStatus.SUBMITTED);
      expect(statuses).toContain(ApplicationStatus.UNDER_REVIEW);
      expect(statuses).toContain(ApplicationStatus.EVALUATION);
      expect(statuses).toContain(ApplicationStatus.APPROVED);
      expect(statuses).toContain(ApplicationStatus.REJECTED);
    });

    it('should transition between valid statuses', () => {
      application.status = ApplicationStatus.SUBMITTED;
      expect(application.status).toBe(ApplicationStatus.SUBMITTED);

      application.status = ApplicationStatus.UNDER_REVIEW;
      expect(application.status).toBe(ApplicationStatus.UNDER_REVIEW);

      application.status = ApplicationStatus.APPROVED;
      expect(application.status).toBe(ApplicationStatus.APPROVED);
    });
  });

  describe('Relationships', () => {
    it('should establish relationship with applicant', () => {
      application.applicant = mockApplicant;
      expect(application.applicant).toBe(mockApplicant);
      expect(application.applicant.applicant_id).toBe('applicant-123');
    });

    it('should establish relationship with license category', () => {
      application.license_category = mockLicenseCategory;
      expect(application.license_category).toBe(mockLicenseCategory);
      expect(application.license_category.license_category_id).toBe('category-123');
    });

    it('should establish relationship with creator', () => {
      application.creator = mockUser;
      expect(application.creator).toBe(mockUser);
      expect(application.creator.user_id).toBe('user-123');
    });
  });

  describe('Progress Tracking', () => {
    it('should track application progress correctly', () => {
      application.current_step = 3;
      application.progress_percentage = 50;
      
      expect(application.current_step).toBe(3);
      expect(application.progress_percentage).toBe(50);
    });

    it('should handle submission timestamp', () => {
      const submissionDate = new Date();
      application.submitted_at = submissionDate;
      application.status = ApplicationStatus.SUBMITTED;
      
      expect(application.submitted_at).toBe(submissionDate);
      expect(application.status).toBe(ApplicationStatus.SUBMITTED);
    });
  });
});
