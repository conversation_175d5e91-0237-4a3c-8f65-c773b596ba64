import { validate } from 'class-validator';
import { Payments, TransactionType, PaymentStatus, PaymentMethod } from '../payments.entity';
import { Applications } from '../applications.entity';
import { Licenses } from '../licenses.entity';
import { Applicants } from '../applicant.entity';
import { User } from '../user.entity';

describe('Payments Entity', () => {
  let payment: Payments;
  let mockApplication: Applications;
  let mockLicense: Licenses;
  let mockApplicant: Applicants;
  let mockUser: User;

  beforeEach(() => {
    mockApplication = {
      application_id: 'app-123',
      application_number: 'APP-2024-001',
    } as Applications;

    mockLicense = {
      license_id: 'license-123',
      license_number: 'LIC-2024-01-001',
    } as Licenses;

    mockApplicant = {
      applicant_id: 'applicant-123',
      name: 'Test Company Ltd',
    } as Applicants;

    mockUser = {
      user_id: 'user-123',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
    } as User;

    payment = new Payments();
    payment.payment_id = 'payment-123';
    payment.transaction_number = 'TXN-2024-001';
    payment.applicant_id = 'applicant-123';
    payment.transaction_type = TransactionType.APPLICATION_FEE;
    payment.amount = 1000.50;
    payment.currency = 'MWK';
    payment.status = PaymentStatus.PENDING;
    payment.description = 'Application fee for license';
    payment.created_by = 'user-123';
    payment.created_at = new Date();
    payment.updated_at = new Date();
  });

  describe('Entity Creation', () => {
    it('should create a payment with valid data', () => {
      expect(payment).toBeDefined();
      expect(payment.payment_id).toBe('payment-123');
      expect(payment.transaction_number).toBe('TXN-2024-001');
      expect(payment.amount).toBe(1000.50);
      expect(payment.currency).toBe('MWK');
      expect(payment.status).toBe(PaymentStatus.PENDING);
    });

    it('should have correct default values', () => {
      const newPayment = new Payments();
      expect(newPayment.currency).toBeUndefined(); // Will be set to 'MWK' by default in entity
      expect(newPayment.payment_method).toBeUndefined();
      expect(newPayment.reference_number).toBeUndefined();
    });
  });

  describe('Validation', () => {
    it('should validate successfully with all required fields', async () => {
      const errors = await validate(payment);
      expect(errors.length).toBe(0);
    });

    it('should fail validation with negative amount', async () => {
      payment.amount = -100;
      const errors = await validate(payment);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'amount')).toBe(true);
    });

    it('should fail validation with invalid currency length', async () => {
      payment.currency = 'INVALID';
      const errors = await validate(payment);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'currency')).toBe(true);
    });

    it('should validate decimal precision for amount', async () => {
      payment.amount = 1000.123; // More than 2 decimal places
      const errors = await validate(payment);
      // Should pass validation as the database will handle precision
      expect(errors.filter(error => error.property === 'amount').length).toBe(0);
    });
  });

  describe('Transaction Types', () => {
    it('should handle all transaction types', () => {
      const types = Object.values(TransactionType);
      expect(types).toContain(TransactionType.APPLICATION_FEE);
      expect(types).toContain(TransactionType.LICENSE_FEE);
      expect(types).toContain(TransactionType.RENEWAL_FEE);
      expect(types).toContain(TransactionType.PENALTY);
      expect(types).toContain(TransactionType.REFUND);
    });

    it('should set different transaction types correctly', () => {
      payment.transaction_type = TransactionType.LICENSE_FEE;
      expect(payment.transaction_type).toBe(TransactionType.LICENSE_FEE);

      payment.transaction_type = TransactionType.REFUND;
      expect(payment.transaction_type).toBe(TransactionType.REFUND);
    });
  });

  describe('Payment Status Management', () => {
    it('should handle all payment statuses', () => {
      const statuses = Object.values(PaymentStatus);
      expect(statuses).toContain(PaymentStatus.PENDING);
      expect(statuses).toContain(PaymentStatus.COMPLETED);
      expect(statuses).toContain(PaymentStatus.FAILED);
      expect(statuses).toContain(PaymentStatus.CANCELLED);
      expect(statuses).toContain(PaymentStatus.REFUNDED);
    });

    it('should transition between payment statuses', () => {
      payment.status = PaymentStatus.PENDING;
      expect(payment.status).toBe(PaymentStatus.PENDING);

      payment.status = PaymentStatus.COMPLETED;
      payment.completed_at = new Date();
      expect(payment.status).toBe(PaymentStatus.COMPLETED);
      expect(payment.completed_at).toBeInstanceOf(Date);
    });
  });

  describe('Payment Methods', () => {
    it('should handle all payment methods', () => {
      const methods = Object.values(PaymentMethod);
      expect(methods).toContain(PaymentMethod.BANK_TRANSFER);
      expect(methods).toContain(PaymentMethod.MOBILE_MONEY);
      expect(methods).toContain(PaymentMethod.CASH);
      expect(methods).toContain(PaymentMethod.CHEQUE);
      expect(methods).toContain(PaymentMethod.ONLINE);
    });

    it('should set payment method correctly', () => {
      payment.payment_method = PaymentMethod.MOBILE_MONEY;
      payment.reference_number = 'MM-REF-123456';
      
      expect(payment.payment_method).toBe(PaymentMethod.MOBILE_MONEY);
      expect(payment.reference_number).toBe('MM-REF-123456');
    });
  });

  describe('Relationships', () => {
    it('should establish relationship with application', () => {
      payment.application_id = 'app-123';
      payment.application = mockApplication;
      expect(payment.application).toBe(mockApplication);
      expect(payment.application.application_id).toBe('app-123');
    });

    it('should establish relationship with license', () => {
      payment.license_id = 'license-123';
      payment.license = mockLicense;
      expect(payment.license).toBe(mockLicense);
      expect(payment.license.license_id).toBe('license-123');
    });

    it('should establish relationship with applicant', () => {
      payment.applicant = mockApplicant;
      expect(payment.applicant).toBe(mockApplicant);
      expect(payment.applicant.applicant_id).toBe('applicant-123');
    });

    it('should handle optional relationships', () => {
      const newPayment = new Payments();
      expect(newPayment.application).toBeUndefined();
      expect(newPayment.license).toBeUndefined();
    });
  });

  describe('Financial Calculations', () => {
    it('should handle currency correctly', () => {
      payment.currency = 'USD';
      payment.amount = 50.75;
      
      expect(payment.currency).toBe('USD');
      expect(payment.amount).toBe(50.75);
    });

    it('should handle large amounts', () => {
      payment.amount = 999999.99;
      expect(payment.amount).toBe(999999.99);
    });

    it('should handle zero amounts for refunds', () => {
      payment.amount = 0;
      payment.transaction_type = TransactionType.REFUND;
      expect(payment.amount).toBe(0);
      expect(payment.transaction_type).toBe(TransactionType.REFUND);
    });
  });
});
