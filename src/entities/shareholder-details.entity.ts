import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Stakeholder } from './stakeholders.entity';

@Entity('shareholder_details')
export class ShareholderDetails {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  shareholder_id: string;

  @Column({ type: 'uuid' })
  stakeholder_id: string;

  @Column({ type: 'int' })
  shareholding_percent: number;

  @Column({ type: 'varchar', length: 300, nullable: true })
  description?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Stakeholder)
  @JoinColumn({ name: 'stakeholder_id' })
  stakeholder: Stakeholder;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.shareholder_id) {
      this.shareholder_id = uuidv4();
    }
  }
}
