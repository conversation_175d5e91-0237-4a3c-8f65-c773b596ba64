import { Column, <PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, BeforeInsert } from "typeorm";
import { v4 as uuidv4 } from 'uuid';

@Entity('postal_codes')
export class PostalCode {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  postal_code_id: string;

  @Column()
  region: string;

  @Column()
  district: string;

  @Column()
  location: string;

  @Column({ unique: true })
  postal_code: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @BeforeInsert()
  generateId() {
    if (!this.postal_code_id) {
      this.postal_code_id = uuidv4();
    }
  }
}
