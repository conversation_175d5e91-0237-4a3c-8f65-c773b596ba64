import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reate<PERSON>ate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  ManyToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Role } from './role.entity';

// Permission constants for reference (optional - can be used or ignored)
export const PERMISSION_NAMES = {
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',

  // Role Management
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',

  // Permission Management
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_READ: 'permission:read',
  PERMISSION_UPDATE: 'permission:update',
  PERMISSION_DELETE: 'permission:delete',

  // License Management
  LICENSE_CREATE: 'license:create',
  LICENSE_READ: 'license:read',
  LICENSE_UPDATE: 'license:update',
  LICENSE_DELETE: 'license:delete',
  LICENSE_APPROVE: 'license:approve',
  LICENSE_REJECT: 'license:reject',

  // License Types
  LICENSE_TYPE_CREATE: 'license_type:create',
  LICENSE_TYPE_READ: 'license_type:read',
  LICENSE_TYPE_UPDATE: 'license_type:update',
  LICENSE_TYPE_DELETE: 'license_type:delete',

  // License Categories
  LICENSE_CATEGORY_CREATE: 'license_category:create',
  LICENSE_CATEGORY_READ: 'license_category:read',
  LICENSE_CATEGORY_UPDATE: 'license_category:update',
  LICENSE_CATEGORY_DELETE: 'license_category:delete',

  // Application Management
  APPLICATION_CREATE: 'application:create',
  APPLICATION_READ: 'application:read',
  APPLICATION_UPDATE: 'application:update',
  APPLICATION_DELETE: 'application:delete',
  APPLICATION_EVALUATE: 'application:evaluate',
  APPLICATION_SUBMIT: 'application:submit',
  APPLICATION_APPROVE: 'application:approve',
  APPLICATION_REJECT: 'application:reject',

  // Financial Management
  FINANCIAL_READ: 'financial:read',
  FINANCIAL_UPDATE: 'financial:update',
  FINANCIAL_REPORTS: 'financial:reports',
  INVOICE_CREATE: 'invoice:create',
  INVOICE_READ: 'invoice:read',
  INVOICE_UPDATE: 'invoice:update',
  INVOICE_DELETE: 'invoice:delete',
  PAYMENT_CREATE: 'payment:create',
  PAYMENT_READ: 'payment:read',
  PAYMENT_UPDATE: 'payment:update',
  PAYMENT_DELETE: 'payment:delete',

  // Document Management
  DOCUMENT_CREATE: 'document:create',
  DOCUMENT_READ: 'document:read',
  DOCUMENT_UPDATE: 'document:update',
  DOCUMENT_DELETE: 'document:delete',
  DOCUMENT_DOWNLOAD: 'document:download',

  // Identification Types
  IDENTIFICATION_TYPE_CREATE: 'identification_type:create',
  IDENTIFICATION_TYPE_READ: 'identification_type:read',
  IDENTIFICATION_TYPE_UPDATE: 'identification_type:update',
  IDENTIFICATION_TYPE_DELETE: 'identification_type:delete',

  // Contact Management
  CONTACT_CREATE: 'contact:create',
  CONTACT_READ: 'contact:read',
  CONTACT_UPDATE: 'contact:update',
  CONTACT_DELETE: 'contact:delete',

  // Applicant Management
  APPLICANT_CREATE: 'applicant:create',
  APPLICANT_READ: 'applicant:read',
  APPLICANT_UPDATE: 'applicant:update',
  APPLICANT_DELETE: 'applicant:delete',

  // Evaluation Management
  EVALUATION_CREATE: 'evaluation:create',
  EVALUATION_READ: 'evaluation:read',
  EVALUATION_UPDATE: 'evaluation:update',
  EVALUATION_DELETE: 'evaluation:delete',
  EVALUATION_SUBMIT: 'evaluation:submit',

  // Notification Management
  NOTIFICATION_CREATE: 'notification:create',
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_UPDATE: 'notification:update',
  NOTIFICATION_DELETE: 'notification:delete',
  NOTIFICATION_SEND: 'notification:send',

  // System Administration
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_AUDIT: 'system:audit',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_MAINTENANCE: 'system:maintenance',

  // Reports
  REPORT_GENERATE: 'report:generate',
  REPORT_VIEW: 'report:view',
  REPORT_EXPORT: 'report:export',
  REPORT_SCHEDULE: 'report:schedule',
} as const;

@Entity('permissions')
export class Permission {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  permission_id: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
  })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  description: string;

  @Column({ type: 'varchar', length: 100 })
  category: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];

  @BeforeInsert()
  generateId() {
    if (!this.permission_id) {
      this.permission_id = uuidv4();
    }
  }
}
