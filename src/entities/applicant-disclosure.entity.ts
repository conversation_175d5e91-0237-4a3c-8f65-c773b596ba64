import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON><PERSON><PERSON>ate<PERSON><PERSON><PERSON>n,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';

@Entity('applicant_disclosure')
export class ApplicantDisclosure {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  applicant_disclosure_id: string;

  @Column({ type: 'uuid' })
  applicant_id: string;

  @Column({ type: 'text', nullable: true })
  censured?: string;

  @Column({ type: 'text', nullable: true })
  disciplined?: string;

  @Column({ type: 'text', nullable: true })
  penalized?: string;

  @Column({ type: 'text', nullable: true })
  suspended?: string;

  @Column({ type: 'text', nullable: true })
  prosecuted?: string;

  @Column({ type: 'text', nullable: true })
  convicted_warned_conduct?: string;

  @Column({ type: 'text', nullable: true })
  investigated_subjected?: string;

  @Column({ type: 'text', nullable: true })
  failed_debt_issued?: string;

  @Column({ type: 'text', nullable: true })
  litigation?: string;

  @Column({ type: 'text', nullable: true })
  adjudged_insolvent?: string;

  @Column({ type: 'text', nullable: true })
  creditor_compromise?: string;

  @Column({ type: 'text', nullable: true })
  liquidator_receiver_property_judicial_manager?: string;

  @Column({ type: 'text', nullable: true })
  voluntary_winding_up?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.applicant_disclosure_id) {
      this.applicant_disclosure_id = uuidv4();
    }
  }
}
