import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEmail, IsOptional, IsUUID, Length, Matches } from 'class-validator';
import { User } from './user.entity';

@Entity('contacts')
export class Contacts {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  contact_id: string;

  @Column({ type: 'varchar', length: 20 })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  telephone: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsEmail()
  @Length(1, 255)
  email?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'varchar', default: 'user', nullable: true})
  entity_type:string;
  
  @Column({ type: 'varchar', nullable: true })
  entity_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.contact_id) {
      this.contact_id = uuidv4();
    }
  }
}
