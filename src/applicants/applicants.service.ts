import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Applicants } from '../entities/applicant.entity';
import { CreateApplicantDto } from '../dto/applicant/create-applicant.dto';
import { UpdateApplicantDto } from '../dto/applicant/update-applicant.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { PolymorphicService } from '../common/services/polymorphic.service';

@Injectable()
export class ApplicantsService {
  constructor(
    @InjectRepository(Applicants)
    private applicantsRepository: Repository<Applicants>,
    private polymorphicService: PolymorphicService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Applicants> = {
    sortableColumns: ['created_at', 'updated_at', 'name', 'business_registration_number'],
    searchableColumns: ['name', 'business_registration_number', 'tpin', 'email'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: [ 'creator', 'updater'],
  };

  async create(createApplicantDto: CreateApplicantDto, createdBy: string): Promise<Applicants> {
    // Check if business registration number already exists
    const existingByRegNumber = await this.applicantsRepository.findOne({
      where: { business_registration_number: createApplicantDto.business_registration_number },
    });

    if (existingByRegNumber) {
      throw new ConflictException('Business registration number already exists');
    }

    // Check if TPIN already exists
    const existingByTpin = await this.applicantsRepository.findOne({
      where: { tpin: createApplicantDto.tpin },
    });

    if (existingByTpin) {
      throw new ConflictException('TPIN already exists');
    }

    const applicant = this.applicantsRepository.create({
      ...createApplicantDto,
      created_by: createdBy,
    });

    return this.applicantsRepository.save(applicant);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Applicants>> {
    return paginate(query, this.applicantsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Applicants> {
    const applicant = await this.applicantsRepository.findOne({
      where: { applicant_id: id },
      relations: ['creator', 'updater'],
    });

    if (!applicant) {
      throw new NotFoundException(`Applicant with ID ${id} not found`);
    }

    return applicant;
  }

  async findByBusinessRegistrationNumber(businessRegistrationNumber: string): Promise<Applicants | null> {
    return this.applicantsRepository.findOne({
      where: { business_registration_number: businessRegistrationNumber },
      relations: ['creator', 'updater'],
    });
  }

  async findByTpin(tpin: string): Promise<Applicants | null> {
    return this.applicantsRepository.findOne({
      where: { tpin },
      relations: ['creator', 'updater'],
    });
  }

  async update(id: string, updateApplicantDto: UpdateApplicantDto, updatedBy: string): Promise<Applicants> {
    const applicant = await this.findOne(id);

    // Check for business registration number conflicts
    if (updateApplicantDto.business_registration_number && 
        updateApplicantDto.business_registration_number !== applicant.business_registration_number) {
      const existingByRegNumber = await this.applicantsRepository.findOne({
        where: { business_registration_number: updateApplicantDto.business_registration_number },
      });

      if (existingByRegNumber) {
        throw new ConflictException('Business registration number already exists');
      }
    }

    // Check for TPIN conflicts
    if (updateApplicantDto.tpin && updateApplicantDto.tpin !== applicant.tpin) {
      const existingByTpin = await this.applicantsRepository.findOne({
        where: { tpin: updateApplicantDto.tpin },
      });

      if (existingByTpin) {
        throw new ConflictException('TPIN already exists');
      }
    }

    Object.assign(applicant, updateApplicantDto, { updated_by: updatedBy });
    return this.applicantsRepository.save(applicant);
  }

  async remove(id: string): Promise<void> {
    const applicant = await this.findOne(id);
    await this.applicantsRepository.softDelete(applicant.applicant_id);
  }

  async search(searchTerm: string): Promise<Applicants[]> {
    return this.applicantsRepository
      .createQueryBuilder('applicant')
      .leftJoinAndSelect('applicant.creator', 'creator')
      .leftJoinAndSelect('applicant.updater', 'updater')
      .where('applicant.name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('applicant.business_registration_number LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('applicant.tpin LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('applicant.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('applicant.created_at', 'DESC')
      .limit(20)
      .getMany();
  }

  /**
   * Get applicant with all related data (addresses and contacts) using polymorphic relationships
   */
  async findOneWithRelatedData(id: string): Promise<{
    applicant: Applicants;
    addresses: any[];
    contacts: any[];
    primaryAddress: any;
    primaryContact: any;
  }> {
    const applicant = await this.findOne(id);
    const relatedData = await this.polymorphicService.getEntityRelatedData('applicant', id);

    return {
      applicant,
      ...relatedData,
    };
  }

  /**
   * Create address for applicant using polymorphic relationship
   */
  async createAddressForApplicant(
    applicantId: string,
    addressData: any,
    createdBy: string,
  ): Promise<any> {
    // Verify applicant exists
    await this.findOne(applicantId);

    return this.polymorphicService.createAddressForEntity(
      'applicant',
      applicantId,
      addressData,
      createdBy,
    );
  }

  /**
   * Create contact person for applicant using polymorphic relationship
   */
  async createContactPersonForApplicant(
    applicantId: string,
    contactData: any,
    createdBy: string,
  ): Promise<any> {
    // Verify applicant exists
    await this.findOne(applicantId);

    return this.polymorphicService.createContactPersonForEntity(
      'applicant',
      applicantId,
      contactData,
      createdBy,
    );
  }
}
