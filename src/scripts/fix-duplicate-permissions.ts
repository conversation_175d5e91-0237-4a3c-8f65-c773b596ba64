import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function fixDuplicatePermissions() {
  // Create data source with minimal configuration
  const dataSource = new DataSource({
    type: process.env.DB_DRIVER as any,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    entities: [],
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // Step 1: Find all permissions with empty or null names using raw SQL
    console.log('🔍 Checking for duplicate/empty permission names...');

    const emptyPermissions = await dataSource.query(
      `SELECT permission_id, name, description FROM permissions WHERE name = '' OR name IS NULL`
    );

    console.log(`Found ${emptyPermissions.length} permissions with empty/null names`);

    if (emptyPermissions.length > 0) {
      console.log('📋 Empty permissions found:');
      emptyPermissions.forEach((perm: any, index: number) => {
        console.log(`  ${index + 1}. ID: ${perm.permission_id}, Name: "${perm.name}", Description: "${perm.description}"`);
      });

      // Step 2: Delete duplicate empty permissions (keep only one if needed)
      if (emptyPermissions.length > 1) {
        console.log('🗑️ Removing duplicate empty permissions...');

        // Keep the first one, delete the rest
        const toDelete = emptyPermissions.slice(1);

        for (const perm of toDelete) {
          await dataSource.query(
            `DELETE FROM permissions WHERE permission_id = ?`,
            [perm.permission_id]
          );
          console.log(`   Deleted permission with ID: ${perm.permission_id}`);
        }
      }

      // Step 3: Update the remaining empty permission with a proper name
      const remainingEmpty = await dataSource.query(
        `SELECT permission_id FROM permissions WHERE name = '' OR name IS NULL LIMIT 1`
      );

      if (remainingEmpty.length > 0) {
        console.log('🔧 Updating remaining empty permission with proper name...');
        await dataSource.query(
          `UPDATE permissions SET name = ?, description = ?, category = ? WHERE permission_id = ?`,
          ['system:placeholder', 'Placeholder permission (auto-generated to fix duplicate issue)', 'System', remainingEmpty[0].permission_id]
        );
        console.log('   Updated empty permission with name: system:placeholder');
      }
    }

    // Step 4: Check for other duplicate names using raw SQL
    console.log('🔍 Checking for other duplicate permission names...');

    const duplicates = await dataSource.query(
      `SELECT name, COUNT(*) as count FROM permissions
       WHERE name IS NOT NULL AND name != ''
       GROUP BY name
       HAVING COUNT(*) > 1`
    );

    if (duplicates.length > 0) {
      console.log('⚠️ Found other duplicate permission names:');
      duplicates.forEach((dup: any) => {
        console.log(`  Name: "${dup.name}" appears ${dup.count} times`);
      });

      // Handle other duplicates
      for (const dup of duplicates) {
        const duplicatePerms = await dataSource.query(
          `SELECT permission_id, name FROM permissions WHERE name = ?`,
          [dup.name]
        );

        console.log(`🔧 Fixing duplicates for "${dup.name}"...`);

        // Keep the first one, rename the rest
        for (let i = 1; i < duplicatePerms.length; i++) {
          const perm = duplicatePerms[i];
          const newName = `${dup.name}_duplicate_${i}`;
          await dataSource.query(
            `UPDATE permissions SET name = ? WHERE permission_id = ?`,
            [newName, perm.permission_id]
          );
          console.log(`   Renamed duplicate to: ${newName}`);
        }
      }
    }

    console.log('✅ Duplicate permission cleanup completed');

    // Step 5: Verify no duplicates remain using raw SQL
    const finalCheck = await dataSource.query(
      `SELECT name, COUNT(*) as count FROM permissions
       GROUP BY name
       HAVING COUNT(*) > 1`
    );

    if (finalCheck.length === 0) {
      console.log('✅ No duplicate permission names found - ready for unique index creation');
    } else {
      console.log('⚠️ Still found duplicates:');
      finalCheck.forEach((dup: any) => {
        console.log(`  Name: "${dup.name}" appears ${dup.count} times`);
      });
    }

  } catch (error) {
    console.error('❌ Error fixing duplicate permissions:', error);
    throw error;
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  fixDuplicatePermissions()
    .then(() => {
      console.log('🎉 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { fixDuplicatePermissions };
