import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function fixRolePermissions() {
  // Create data source with minimal configuration
  const dataSource = new DataSource({
    type: process.env.DB_DRIVER as any,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    entities: [],
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // Step 1: Find orphaned role_permissions entries
    console.log('🔍 Checking for orphaned role_permissions entries...');
    
    const orphanedEntries = await dataSource.query(`
      SELECT rp.role_id, rp.permission_id 
      FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_id = p.permission_id 
      WHERE p.permission_id IS NULL
    `);

    console.log(`Found ${orphanedEntries.length} orphaned role_permissions entries`);

    if (orphanedEntries.length > 0) {
      console.log('📋 Orphaned entries:');
      orphanedEntries.forEach((entry: any, index: number) => {
        console.log(`  ${index + 1}. Role ID: ${entry.role_id}, Permission ID: ${entry.permission_id}`);
      });

      // Step 2: Delete orphaned entries
      console.log('🗑️ Removing orphaned role_permissions entries...');
      
      for (const entry of orphanedEntries) {
        await dataSource.query(
          `DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?`,
          [entry.role_id, entry.permission_id]
        );
        console.log(`   Deleted role_permission: role_id=${entry.role_id}, permission_id=${entry.permission_id}`);
      }
    }

    // Step 3: Check for orphaned roles as well
    console.log('🔍 Checking for orphaned role references in role_permissions...');
    
    const orphanedRoles = await dataSource.query(`
      SELECT rp.role_id, rp.permission_id 
      FROM role_permissions rp 
      LEFT JOIN roles r ON rp.role_id = r.role_id 
      WHERE r.role_id IS NULL
    `);

    console.log(`Found ${orphanedRoles.length} orphaned role references in role_permissions`);

    if (orphanedRoles.length > 0) {
      console.log('📋 Orphaned role references:');
      orphanedRoles.forEach((entry: any, index: number) => {
        console.log(`  ${index + 1}. Role ID: ${entry.role_id}, Permission ID: ${entry.permission_id}`);
      });

      // Delete orphaned role references
      console.log('🗑️ Removing orphaned role references...');
      
      for (const entry of orphanedRoles) {
        await dataSource.query(
          `DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?`,
          [entry.role_id, entry.permission_id]
        );
        console.log(`   Deleted role_permission: role_id=${entry.role_id}, permission_id=${entry.permission_id}`);
      }
    }

    // Step 4: Verify data integrity
    console.log('🔍 Verifying role_permissions data integrity...');
    
    const finalCheck = await dataSource.query(`
      SELECT COUNT(*) as count FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_id = p.permission_id 
      LEFT JOIN roles r ON rp.role_id = r.role_id 
      WHERE p.permission_id IS NULL OR r.role_id IS NULL
    `);

    if (finalCheck[0].count === 0) {
      console.log('✅ All role_permissions entries have valid foreign key references');
    } else {
      console.log(`⚠️ Still found ${finalCheck[0].count} invalid role_permissions entries`);
    }

    // Step 5: Show current valid role_permissions count
    const validCount = await dataSource.query(`
      SELECT COUNT(*) as count FROM role_permissions rp 
      INNER JOIN permissions p ON rp.permission_id = p.permission_id 
      INNER JOIN roles r ON rp.role_id = r.role_id
    `);

    console.log(`✅ Total valid role_permissions entries: ${validCount[0].count}`);

    console.log('✅ Role permissions cleanup completed');

  } catch (error) {
    console.error('❌ Error fixing role permissions:', error);
    throw error;
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  fixRolePermissions()
    .then(() => {
      console.log('🎉 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { fixRolePermissions };
