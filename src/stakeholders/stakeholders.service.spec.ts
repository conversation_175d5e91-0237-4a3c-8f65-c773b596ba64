import { Test, TestingModule } from '@nestjs/testing';
import { StakeholdersService } from './stakeholders.service';
import { Repository } from 'typeorm';
import { Stakeholder } from 'src/entities/stakeholders.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateStakeholderDto } from "src/dto/stakeholder/create-stakeholder.dto";
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
import { NotFoundException } from '@nestjs/common';

const mockStakeholder = {
  stakeholder_id: 'uuid-stakeholder',
  applicant_id: 'uuid-applicant',
  contact_id: 'uuid-contact',
  created_by: 'uuid-user',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  middle_name: '<PERSON>',
  nationality: 'Malawian',
  position: 'CEO',
  profile: 'CEO of the org',
  cv_document_id: 'uuid-doc',
};

describe('StakeholdersService', () => {
  let service: StakeholdersService;
  let repo: Repository<Stakeholder>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StakeholdersService,
        {
          provide: getRepositoryToken(Stakeholder),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<StakeholdersService>(StakeholdersService);
    repo = module.get<Repository<Stakeholder>>(getRepositoryToken(Stakeholder));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and return a stakeholder', async () => {
      const dto: CreateStakeholderDto = {
        applicant_id: mockStakeholder.applicant_id,
        contact_id: mockStakeholder.contact_id,
        first_name: mockStakeholder.first_name,
        last_name: mockStakeholder.last_name,
        middle_name: mockStakeholder.middle_name,
        nationality: mockStakeholder.nationality,
        position: mockStakeholder.position as any,
        profile: mockStakeholder.profile,
        cv_document_id: mockStakeholder.cv_document_id,
      };

      jest.spyOn(repo, 'create').mockReturnValue(mockStakeholder as any);
      jest.spyOn(repo, 'save').mockResolvedValue(mockStakeholder as any);

      const result = await service.create(dto, mockStakeholder.created_by);
      expect(result).toEqual(mockStakeholder);
    });
  });

  describe('findAll', () => {
    it('should return all stakeholders', async () => {
      jest.spyOn(repo, 'find').mockResolvedValue([mockStakeholder] as any);
      const result = await service.findAll();
      expect(result).toEqual([mockStakeholder]);
    });
  });

  describe('findOne', () => {
    it('should return a stakeholder', async () => {
      jest.spyOn(repo, 'findOne').mockResolvedValue(mockStakeholder as any);
      const result = await service.findOne(mockStakeholder.stakeholder_id);
      expect(result).toEqual(mockStakeholder);
    });

    it('should throw NotFoundException if not found', async () => {
      jest.spyOn(repo, 'findOne').mockResolvedValue(null);
      await expect(service.findOne('invalid-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update and return the stakeholder', async () => {
      const updateDto: UpdateStakeholderDto = { first_name: 'Jane' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockStakeholder as any);
      jest.spyOn(repo, 'save').mockResolvedValue({ ...mockStakeholder, ...updateDto } as any);

      const result = await service.update(mockStakeholder.stakeholder_id, updateDto, 'uuid-user');
      expect(result.first_name).toBe('Jane');
    });
  });

  describe('softDelete', () => {
    it('should soft remove the stakeholder', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(mockStakeholder as any);
      jest.spyOn(repo, 'softRemove').mockResolvedValue(mockStakeholder as any);

      await expect(service.softDelete(mockStakeholder.stakeholder_id)).resolves.toBeUndefined();
    });
  });
});
