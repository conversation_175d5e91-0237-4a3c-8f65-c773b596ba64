import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ConsumerAffairsComplaintService } from './consumer-affairs-complaint.service';
import {
  CreateConsumerAffairsComplaintDto,
  UpdateConsumerAffairsComplaintDto,
  ConsumerAffairsComplaintFilterDto,
  UpdateConsumerAffairsComplaintStatusDto,
} from './consumer-affairs-complaint.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { ConsumerAffairsComplaint } from './consumer-affairs-complaint.entity';

@Controller('consumer-affairs-complaints')
@ApiTags('Consumer Affairs Complaints')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
export class ConsumerAffairsComplaintController {
  constructor(
    private readonly complaintService: ConsumerAffairsComplaintService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new consumer affairs complaint' })
  @ApiResponse({ status: 201, description: 'Complaint created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(FilesInterceptor('attachments', 5)) // Allow up to 5 files
  async create(
    @Body(ValidationPipe) createDto: CreateConsumerAffairsComplaintDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.create(createDto, req.user.user_id);

    // Handle file uploads if any
    if (files && files.length > 0) {
      // TODO: Implement file upload logic
      // This would typically involve saving files to storage and creating attachment records
    }

    return complaint;
  }

  @Get()
  @ApiOperation({ summary: 'Get all consumer affairs complaints with pagination' })
  @ApiResponse({ status: 200, description: 'Complaints retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Request() req: any,
  ) {
    return this.complaintService.findAll(
      query,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific consumer affairs complaint by ID' })
  @ApiResponse({ status: 200, description: 'Complaint retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Complaint not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    return this.complaintService.findOne(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );
  }

  @Put(':id')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateConsumerAffairsComplaintDto,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.update(
      id,
      updateDto,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Consumer affairs complaint updated successfully',
      data: complaint,
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    await this.complaintService.delete(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Consumer affairs complaint deleted successfully',
    };
  }

  // Staff-only endpoints
  @Put(':id/status')
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) statusDto: UpdateConsumerAffairsComplaintStatusDto,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.updateStatus(
      id,
      statusDto,
      req.user.user_id
    );

    return {
      success: true,
      message: 'Complaint status updated successfully',
      data: complaint,
    };
  }

  @Put(':id/assign')
  async assignComplaint(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('assigned_to', ParseUUIDPipe) assignedTo: string,
    @Request() req: any,
  ) {
    const complaint = await this.complaintService.update(
      id,
      { assigned_to: assignedTo },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Complaint assigned successfully',
      data: complaint,
    };
  }

  @Get('stats/summary')
  async getStatsSummary(@Request() req: any) {
    // TODO: Implement statistics summary
    // This would return counts by status, category, priority, etc.
    return {
      success: true,
      message: 'Statistics retrieved successfully',
      data: {
        total: 0,
        by_status: {},
        by_category: {},
        by_priority: {},
      },
    };
  }

  @Get('export/csv')
  async exportToCsv(
    @Query(ValidationPipe) filterDto: ConsumerAffairsComplaintFilterDto,
    @Request() req: any,
  ) {
    // TODO: Implement CSV export functionality
    return {
      success: true,
      message: 'Export functionality not yet implemented',
    };
  }

  // File upload endpoint for adding attachments to existing complaints
  @Post(':id/attachments')
  @UseInterceptors(FilesInterceptor('files', 5))
  async addAttachments(
    @Param('id', ParseUUIDPipe) id: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    // TODO: Implement file upload and attachment creation
    return {
      success: true,
      message: 'File upload functionality not yet implemented',
    };
  }

  @Delete(':id/attachments/:attachmentId')
  async deleteAttachment(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('attachmentId', ParseUUIDPipe) attachmentId: string,
    @Request() req: any,
  ) {
    // TODO: Implement attachment deletion
    return {
      success: true,
      message: 'Attachment deletion functionality not yet implemented',
    };
  }
}
