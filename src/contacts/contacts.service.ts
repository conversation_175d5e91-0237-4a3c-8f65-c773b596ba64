import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Contacts } from '../entities/contacts.entity';
import { CreateContactDto } from '../dto/contact/create-contact.dto';
import { UpdateContactDto } from '../dto/contact/update-contact.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';

@Injectable()
export class ContactsService {
  constructor(
    @InjectRepository(Contacts)
    private contactsRepository: Repository<Contacts>,
  ) {}

  private readonly paginateConfig: PaginateConfig<Contacts> = {
    sortableColumns: ['created_at', 'updated_at', 'telephone', 'email'],
    searchableColumns: ['telephone', 'email'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['creator', 'updater'],
  };

  async create(createContactDto: CreateContactDto, createdBy: string): Promise<Contacts> {
    const contact = this.contactsRepository.create({
      ...createContactDto,
      created_by: createdBy,
    });

    return this.contactsRepository.save(contact);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Contacts>> {
    return paginate(query, this.contactsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Contacts> {
    const contact = await this.contactsRepository.findOne({
      where: { contact_id: id },
      relations: ['creator', 'updater'],
    });

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    return contact;
  }

  async findByTelephone(telephone: string): Promise<Contacts | null> {
    return this.contactsRepository.findOne({
      where: { telephone },
      relations: ['creator', 'updater'],
    });
  }

  async findByEmail(email: string): Promise<Contacts | null> {
    return this.contactsRepository.findOne({
      where: { email },
      relations: ['creator', 'updater'],
    });
  }

  async update(id: string, updateContactDto: UpdateContactDto, updatedBy: string): Promise<Contacts> {
    const contact = await this.findOne(id);

    Object.assign(contact, updateContactDto, { updated_by: updatedBy });
    return this.contactsRepository.save(contact);
  }

  async remove(id: string): Promise<void> {
    const contact = await this.findOne(id);
    await this.contactsRepository.softDelete(contact.contact_id);
  }

  async search(searchTerm: string): Promise<Contacts[]> {
    return this.contactsRepository
      .createQueryBuilder('contact')
      .where('contact.telephone LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('contact.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('contact.created_at', 'DESC')
      .limit(20)
      .getMany();
  }

  async getContactsWithEmail(): Promise<Contacts[]> {
    return this.contactsRepository
      .createQueryBuilder('contact')
      .leftJoinAndSelect('contact.creator', 'creator')
      .leftJoinAndSelect('contact.updater', 'updater')
      .where('contact.email IS NOT NULL')
      .andWhere('contact.email != :empty', { empty: '' })
      .orderBy('contact.created_at', 'DESC')
      .getMany();
  }

  async getContactsWithoutEmail(): Promise<Contacts[]> {
    return this.contactsRepository
      .createQueryBuilder('contact')
      .leftJoinAndSelect('contact.creator', 'creator')
      .leftJoinAndSelect('contact.updater', 'updater')
      .where('contact.email IS NULL')
      .orWhere('contact.email = :empty', { empty: '' })
      .orderBy('contact.created_at', 'DESC')
      .getMany();
  }
}
