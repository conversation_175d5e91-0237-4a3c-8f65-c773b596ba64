import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { ApplicationTaskHelperService } from './application-task-helper.service';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    private applicationTaskHelper: ApplicationTaskHelperService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Applications> = {
    sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
    searchableColumns: ['application_number', 'status'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater', 'assignee'],
    filterableColumns: {
      status: true,
      created_by: true,
      license_category_id: true,
      applicant_id: true,
      assigned_to: true,
      'license_category.license_type_id': true,
    },
  };

  async create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications> {
    // Check if application number already exists
    const existingApplication = await this.applicationsRepository.findOne({
      where: { application_number: createApplicationDto.application_number },
    });

    if (existingApplication) {
      throw new ConflictException('Application number already exists');
    }

    const application = this.applicationsRepository.create({
      ...createApplicationDto,
      current_step: createApplicationDto.current_step || 1,
      progress_percentage: createApplicationDto.progress_percentage || 0,
      status: createApplicationDto.status || 'draft',
      created_by: createdBy,
    });

    return this.applicationsRepository.save(application);
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their created applications only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.applicationsRepository, this.paginateConfig);
    }

    // For department users, exclude draft applications unless explicitly requested
    const includeDraftValue = query.filter?.['include_draft'];
    const shouldIncludeDrafts = includeDraftValue === 'true' ||
                               (typeof includeDraftValue === 'boolean' && includeDraftValue === true);

    if (shouldIncludeDrafts) {
      // Include all applications including drafts
      return paginate(query, this.applicationsRepository, this.paginateConfig);
    } else {
      // Exclude draft applications by using a custom query builder
      const queryBuilder = this.applicationsRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.applicant', 'applicant')
        .leftJoinAndSelect('application.license_category', 'license_category')
        .leftJoinAndSelect('license_category.license_type', 'license_type')
        .leftJoinAndSelect('application.creator', 'creator')
        .leftJoinAndSelect('application.updater', 'updater')
        .leftJoinAndSelect('application.assignee', 'assignee')
        .where('application.status != :draftStatus', { draftStatus: 'draft' });

      // Apply additional filters from the query
      if (query.filter) {
        Object.entries(query.filter).forEach(([key, value]) => {
          if (key !== 'include_draft' && value !== undefined && value !== '') {
            if (key.includes('.')) {
              // Handle nested filters like 'license_category.license_type_id'
              queryBuilder.andWhere(`${key} = :${key.replace('.', '_')}`, { [key.replace('.', '_')]: value });
            } else {
              queryBuilder.andWhere(`application.${key} = :${key}`, { [key]: value });
            }
          }
        });
      }

      // Apply search if provided
      if (query.search) {
        queryBuilder.andWhere(
          '(application.application_number LIKE :search OR application.status LIKE :search)',
          { search: `%${query.search}%` }
        );
      }

      return paginate(query, queryBuilder, this.paginateConfig);
    }
  }

  async findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    // This method specifically filters applications by the authenticated user
    // It's used for customer-facing endpoints like /user-applications
    return paginate(query, this.applicationsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Applications> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: id },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async findByApplicant(applicantId: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { applicant_id: applicantId },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByStatus(status: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    // If updating application number, check for conflicts
    if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
      const existingApplication = await this.applicationsRepository.findOne({
        where: { application_number: updateApplicationDto.application_number },
      });

      if (existingApplication) {
        throw new ConflictException('Application number already exists');
      }
    }

    Object.assign(application, updateApplicationDto, { updated_by: updatedBy });

    // Set submitted_at if status is being changed to submitted for the first time
    if (updateApplicationDto.status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    // Check if status has changed (either through direct status update or other means)
    const currentStatus = savedApplication.status;
    if (currentStatus !== previousStatus) {
      await this.applicationTaskHelper.handleApplicationSubmission(
        application.application_id,
        previousStatus,
        currentStatus,
        updatedBy
      );
    }

    return savedApplication;
  }

  async remove(id: string): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationsRepository.softDelete(application.application_id);
  }

  async updateStatus(id: string, status: string, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    application.status = status;
    application.updated_by = updatedBy;

    if (status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    await this.applicationTaskHelper.handleApplicationSubmission(
      application.application_id,
      previousStatus,
      status,
      updatedBy
    );

    return savedApplication;
  }

  async updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    application.current_step = currentStep;
    application.progress_percentage = progressPercentage;
    application.updated_by = updatedBy;

    return this.applicationsRepository.save(application);
  }

  async getApplicationStats(): Promise<any> {
    const stats = await this.applicationsRepository
      .createQueryBuilder('application')
      .select('application.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('application.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }

  async assignApplication(applicationId: string, assignedTo: string, assignedBy: string): Promise<Applications> {
    const application = await this.findOne(applicationId);
    application.assigned_to = assignedTo;
    application.assigned_at = new Date();
    application.updated_by = assignedBy;

    return this.applicationsRepository.save(application);
  }

  async getUnassignedApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  async getAssignedApplications(userId: string, query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  // Debug method to get all applications without filtering
  async findAllDebug(query: PaginateQuery): Promise<Paginated<Applications>> {
    const count = await this.applicationsRepository.count();
    // Get status distribution
    const statusStats = await this.applicationsRepository
      .createQueryBuilder('app')
      .select('app.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('app.status')
      .getRawMany();
        
    const debugConfig: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      // Remove any status filtering for debug
    };
    
    return paginate(query, this.applicationsRepository, debugConfig);
  }
}
