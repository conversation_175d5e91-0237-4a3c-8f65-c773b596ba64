import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
} from '@nestjs/common';
import { LegalHistoryService } from './legal-history.service';
import { CreateLegalHistoryDto } from '../dto/legal-history/create-legal-history.dto';
import { UpdateLegalHistoryDto } from '../dto/legal-history/update-legal-history.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { LegalHistory } from '../entities/legal-history.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Legal History')
@Controller('legal-history')
export class LegalHistoryController {
  constructor(private readonly legalHistoryService: LegalHistoryService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new legal history record' })
  @ApiBody({ type: CreateLegalHistoryDto, description: 'Create legal history DTO' })
  @ApiResponse({ status: 201, description: 'Legal history created successfully', type: LegalHistory })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  create(@Body() createDto: CreateLegalHistoryDto, @Request() req: any) {
    return this.legalHistoryService.create(createDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all legal history records' })
  @ApiResponse({ status: 200, description: 'List of legal history records', type: [LegalHistory] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findAll() {
    return this.legalHistoryService.findAll();
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get legal history by application ID' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiResponse({ status: 200, description: 'Legal history found', type: LegalHistory })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findByApplication(@Param('applicationId') applicationId: string) {
    return this.legalHistoryService.findByApplication(applicationId);
  }

  @Post('application/:applicationId')
  @ApiOperation({ summary: 'Create or update legal history for application' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiBody({ type: CreateLegalHistoryDto, description: 'Legal history data (without application_id)' })
  @ApiResponse({ status: 200, description: 'Legal history created or updated', type: LegalHistory })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  createOrUpdateForApplication(
    @Param('applicationId') applicationId: string, 
    @Body() createDto: Omit<CreateLegalHistoryDto, 'application_id'>, 
    @Request() req: any
  ) {
    return this.legalHistoryService.createOrUpdate(applicationId, createDto, req.user.userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get legal history by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'Legal history UUID' })
  @ApiResponse({ status: 200, description: 'Legal history found', type: LegalHistory })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findOne(@Param('id') id: string) {
    return this.legalHistoryService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update legal history by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiBody({ type: UpdateLegalHistoryDto, description: 'Update legal history DTO' })
  @ApiResponse({ status: 200, description: 'Legal history updated', type: LegalHistory })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  update(@Param('id') id: string, @Body() updateDto: UpdateLegalHistoryDto, @Request() req: any) {
    return this.legalHistoryService.update(id, updateDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete legal history by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiResponse({ status: 204, description: 'Legal history deleted' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  remove(@Param('id') id: string) {
    return this.legalHistoryService.softDelete(id);
  }
}
