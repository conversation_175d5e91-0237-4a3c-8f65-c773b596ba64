import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PostalCode } from './entities/postal-code.entity';
import { SearchPostalCodeDTO } from './dto/postal-code/search.dto';
import { StandardResponse } from './common/interceptors/response.interceptor'; // adjust path if needed

@Injectable()
export class AppService {
  constructor(
    @InjectRepository(PostalCode)
    private readonly postalCodeRepository: Repository<PostalCode>,
  ) {}

    getHello(): string {
    return 'Hello World!';
  }

  async searchPostalCodes(searchCode: SearchPostalCodeDTO): Promise<StandardResponse<PostalCode[]>> {
    const { region, district, location, postal_code } = searchCode;

    if (!region) {
      throw new BadRequestException({
        success: false,
        message: 'Region is required!',
        data: null,
      });
    }

    const query = this.postalCodeRepository.createQueryBuilder('postal');

    query.where('LOWER(postal.region) = :region', { region: region.toLowerCase() });

    if (district?.trim()) {
      query.andWhere('LOWER(postal.district) = :district', {
        district: district.toLowerCase(),
      });
    }

    if (location?.trim()) {
      query.andWhere('LOWER(postal.location) = :location', {
        location: location.toLowerCase(),
      });
    }

    if (postal_code?.trim()) {
      query.andWhere('postal.postal_code = :postal_code', { postal_code });
    }

    const results = await query
      .orderBy('postal.region', 'ASC')
      .addOrderBy('postal.district', 'ASC')
      .addOrderBy('postal.location', 'ASC')
      .getMany();

    return {
      success: true,
      message: results.length
        ? 'Postal codes retrieved successfully'
        : 'No postal codes found for the given filters',
      data: results,
      meta: { total: results.length },
      timestamp: new Date().toISOString(),
      path: '/postal-codes/search',
      statusCode: 200,
    };
  }
}
