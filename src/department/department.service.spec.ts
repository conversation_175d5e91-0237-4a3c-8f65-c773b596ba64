import { Test, TestingModule } from '@nestjs/testing';
import { DepartmentService } from './department.service';
import { Department } from '../entities/department.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';

const mockDepartment = {
  department_id: 'uuid-1234',
  code: 'HR',
  name: 'Human Resources',
  description: 'Handles HR',
  email: '<EMAIL>',
};

describe('DepartmentService', () => {
  let service: DepartmentService;
  let repo: Repository<Department>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DepartmentService,
        {
          provide: getRepositoryToken(Department),
          useValue: {
            find: jest.fn(),
            findOneBy: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            softDelete: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<DepartmentService>(DepartmentService);
    repo = module.get<Repository<Department>>(getRepositoryToken(Department));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and return a department', async () => {
      jest.spyOn(repo, 'save').mockResolvedValue(mockDepartment as any);

      const result = await service.create({
        code: 'HR',
        name: 'Human Resources',
        description: 'Handles HR',
        email: '<EMAIL>',
      });

      expect(repo.save).toHaveBeenCalled();
      expect(result).toEqual(mockDepartment);
    });
  });

  describe('findAll', () => {
    it('should return array of departments', async () => {
      jest.spyOn(repo, 'find').mockResolvedValue([mockDepartment as any]);

      const result = await service.findAll();

      expect(repo.find).toHaveBeenCalled();
      expect(result).toEqual([mockDepartment]);
    });
  });

  describe('findOne', () => {
    it('should return a department if found', async () => {
      jest.spyOn(repo, 'findOneBy').mockResolvedValue(mockDepartment as any);

      const result = await service.findOne('uuid-1234');

      expect(repo.findOneBy).toHaveBeenCalledWith({ department_id: 'uuid-1234' });
      expect(result).toEqual(mockDepartment);
    });

    it('should throw NotFoundException if not found', async () => {
      jest.spyOn(repo, 'findOneBy').mockResolvedValue(null);

      await expect(service.findOne('uuid-1234')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update and return the updated department', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(mockDepartment as any);

      jest.spyOn(repo, 'save').mockImplementation(async (dept) => {
        return {
          ...mockDepartment,       // base existing mock department (full object)
          ...dept,                 // override with updated fields
        };
      });

      const updateDto = { name: 'HR Updated' };
      const result = await service.update('uuid-1234', updateDto);

      expect(service.findOne).toHaveBeenCalledWith('uuid-1234');
      expect(repo.save).toHaveBeenCalled();
      expect(result.name).toEqual('HR Updated');
    });
  });


  describe('remove', () => {
    it('should soft delete the department', async () => {
      jest.spyOn(repo, 'softDelete').mockResolvedValue({ affected: 1 } as any);

      await expect(service.remove('uuid-1234')).resolves.not.toThrow();

      expect(repo.softDelete).toHaveBeenCalledWith('uuid-1234');
    });

    it('should throw NotFoundException if department not found', async () => {
      jest.spyOn(repo, 'softDelete').mockResolvedValue({ affected: 0 } as any);

      await expect(service.remove('uuid-1234')).rejects.toThrow(NotFoundException);
    });
  });
});
