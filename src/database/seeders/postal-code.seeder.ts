import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PostalCode } from '../../entities/postal-code.entity';
import postalCodeData from './data/postal-codes.json';

@Injectable()
export class PostalCodeSeederService {
  constructor(
    @InjectRepository(PostalCode)
    private postalCodeRepository: Repository<PostalCode>,
  ) {}

  async seedPostalCodes(): Promise<void> {
    console.log('🌱 Seeding postal codes...');

    const existing = await this.postalCodeRepository.count();
    if (existing > 0) {
      console.log('✅ Postal codes already exist, skipping...');
      return;
    }

    const inserts = postalCodeData.map((entry) =>
      this.postalCodeRepository.create({
        region: entry.region,
        district: entry.district,
        location: entry.location,
        postal_code: entry.postal_code,
      }),
    );

    await this.postalCodeRepository.save(inserts);
    console.log(`✅ Seeded ${inserts.length} postal codes.`);
  }

  async seedAll(): Promise<void> {
    await this.seedPostalCodes();
  }

  async clearAll(): Promise<void> {
    await this.postalCodeRepository.clear();
    console.log('🗑️ Cleared postal codes');
  }
}
