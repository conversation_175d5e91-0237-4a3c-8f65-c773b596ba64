import { DataSource } from 'typeorm';
import { LicenseTypes } from '../../entities/license-types.entity';
import { LicenseCategories } from '../../entities/license-categories.entity';
import { Applications } from '../../entities/applications.entity';
import { Applicants } from '../../entities/applicant.entity';
import { Documents } from '../../entities/documents.entity';
import { Contacts } from '../../entities/contacts.entity';
import { User } from '../../entities/user.entity';

export const createSeederDataSource = (): DataSource => {
  return new DataSource({
    type: process.env.DB_TYPE as any || 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'macra_db',
    entities: [
      LicenseTypes,
      LicenseCategories,
      Applications,
      Applicants,
      Documents,
      Contacts,
      User,
    ],
    synchronize: false,
    logging: false,
    migrations: ['src/database/migrations/**/*.ts'],
    subscribers: ['src/database/subscribers/**/*.ts'],
  });
};
