#!/usr/bin/env node

import 'reflect-metadata';
import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { createSeederDataSource } from './seeder.config';
import { LicenseTypes } from '../../entities/license-types.entity';
import { LicenseCategories } from '../../entities/license-categories.entity';

// Load environment variables
config();

async function verifySeeding() {
  console.log('🔍 MACRA License System Verification');
  console.log('====================================');

  let dataSource: DataSource | undefined;
  
  try {
    // Create data source
    dataSource = createSeederDataSource();
    
    // Initialize the data source
    console.log('📡 Connecting to database...');
    await dataSource.initialize();
    console.log('✅ Database connection established');
    
    // Get repositories
    const licenseTypeRepository = dataSource.getRepository(LicenseTypes);
    const licenseCategoryRepository = dataSource.getRepository(LicenseCategories);
    
    // Count license types
    const licenseTypeCount = await licenseTypeRepository.count();
    console.log(`\n📋 License Types: ${licenseTypeCount} found`);
    
    if (licenseTypeCount > 0) {
      const licenseTypes = await licenseTypeRepository.find();
      licenseTypes.forEach((type, index) => {
        console.log(`   ${index + 1}. ${type.name} (${type.validity} years validity)`);
      });
    }
    
    // Count license categories
    const licenseCategoryCount = await licenseCategoryRepository.count();
    console.log(`\n📂 License Categories: ${licenseCategoryCount} found`);
    
    if (licenseCategoryCount > 0) {
      const licenseCategories = await licenseCategoryRepository.find({
        relations: ['license_type'],
      });
      
      // Group by license type
      const groupedCategories = licenseCategories.reduce((acc, category) => {
        const typeName = category.license_type?.name || 'Unknown';
        if (!acc[typeName]) {
          acc[typeName] = [];
        }
        acc[typeName].push(category);
        return acc;
      }, {} as Record<string, any[]>);
      
      Object.entries(groupedCategories).forEach(([typeName, categories]) => {
        console.log(`\n   📁 ${typeName} (${categories.length} categories):`);
        categories.forEach((category, index) => {
          console.log(`      ${index + 1}. ${category.name} - K${category.fee}`);
        });
      });
    }
    
    // Summary
    console.log('\n📊 Verification Summary:');
    console.log(`   • License Types: ${licenseTypeCount}`);
    console.log(`   • License Categories: ${licenseCategoryCount}`);
    
    if (licenseTypeCount === 5 && licenseCategoryCount === 21) {
      console.log('\n✅ All seeders verified successfully!');
      console.log('   The license system is ready for use.');
    } else {
      console.log('\n⚠️  Seeding may be incomplete:');
      console.log(`   Expected: 5 license types, 21 categories`);
      console.log(`   Found: ${licenseTypeCount} license types, ${licenseCategoryCount} categories`);
    }
    
  } catch (error) {
    console.error('\n❌ Verification failed:', error);
    process.exit(1);
  } finally {
    if (dataSource && dataSource.isInitialized) {
      console.log('\n📡 Closing database connection...');
      await dataSource.destroy();
      console.log('✅ Database connection closed');
    }
  }
}

// Run verification
verifySeeding();

export { verifySeeding };
