import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from 'src/entities/department.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class DepartmentSeederService {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async seedDepartments(): Promise<void> {
    console.log('🌱 Seeding departments...');

    const existing = await this.departmentRepository.count();
    if (existing > 0) {
      console.log('✅ Departments already exist, skipping...');
      return;
    }

    const departments: Partial<Department>[] = [
      {
        department_id: uuidv4(),
        code: 'PROC',
        name: 'Procurement',
        description: 'Handles procurement and purchasing of goods and services.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'HR',
        name: 'Human Resources',
        description: 'Responsible for recruitment, staff welfare and training.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'ICT',
        name: 'Information and Communication Technology',
        description: 'Manages IT infrastructure and communication systems.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'ADMIN',
        name: 'Administration',
        description: 'Provides administrative support to the organization.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'POST',
        name: 'Postal',
        description: 'Oversees postal services and operations.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'STD',
        name: 'Standards',
        description: 'Ensures compliance with national and international standards.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'AUD',
        name: 'Internal Audit',
        description: 'Conducts internal audits to ensure financial and operational integrity.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'IR',
        name: 'International Relations',
        description: 'Manages relationships with international partners and organizations.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'FIN',
        name: 'Finance',
        description: 'Handles financial planning, budgeting, and accounting.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'NET',
        name: 'Networks',
        description: 'Responsible for network infrastructure and security.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'LEGAL',
        name: 'Legal',
        description: 'Provides legal advice and manages compliance with laws.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'TEL',
        name: 'Telecommunications',
        description: 'Regulates telecommunications services and providers.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'CONAF',
        name: 'Consumer Affairs',
        description: 'Protects consumer rights and addresses complaints.',
        email: '<EMAIL>',
      },
      {
        department_id: uuidv4(),
        code: 'DPA',
        name: 'Data Protection',
        description: 'Ensures data privacy and protection compliance.',
        email: '<EMAIL>',
      },
    ];

    const inserts = departments.map((entry) => this.departmentRepository.create(entry));
    await this.departmentRepository.save(inserts);

    console.log(`✅ Seeded ${inserts.length} departments.`);
  }

  async seedAll(): Promise<void> {
    await this.seedDepartments();
  }

  async clearAll(): Promise<void> {
    await this.departmentRepository.clear();
    console.log('🗑️ Cleared departments');
  }
}
