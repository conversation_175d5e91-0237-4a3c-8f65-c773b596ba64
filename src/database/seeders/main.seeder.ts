import { DataSource } from 'typeorm';
import LicenseTypesSeeder from './license-types.seeder';
import LicenseCategoriesSeeder from './license-categories.seeder';
import { OrganizationSeederService } from "./organizations.seeder";
import { DepartmentSeederService } from './departments.seeder';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class MainSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    console.log('🚀 Starting MACRA License System Seeding...');
    
    try {
      // Run license types seeder first
      console.log('\n📋 Seeding License Types...');
      const licenseTypesSeeder = new LicenseTypesSeeder();
      await licenseTypesSeeder.run(dataSource);
      
      // Run license categories seeder after license types
      console.log('\n📂 Seeding License Categories...');
      const licenseCategoriesSeeder = new LicenseCategoriesSeeder();
      await licenseCategoriesSeeder.run(dataSource);

      // Run organizations seeder after license categories
      // console.log('\n🏢 Seeding Organizations...');
      // const organizationsSeeder = new OrganizationSeederService();
      // await organizationsSeeder.seedOrganizations();

      // Run departments seeder after organizations
      // console.log('\n🏢 Seeding Departments...');
      // const departmentsSeeder = new DepartmentSeederService();
      // await departmentsSeeder.seedDepartments();
      
      console.log('\n✅ MACRA License System Seeding Completed Successfully!');
      console.log('\n📊 Summary:');
      console.log('   • 5 License Types created');
      console.log('   • 21 License Categories created');
      console.log('   • Telecommunications: 5 categories');
      console.log('   • Postal Services: 5 categories');
      console.log('   • Standards Compliance: 4 categories');
      console.log('   • Broadcasting: 4 categories');
      console.log('   • Spectrum Management: 3 categories');
      
    } catch (error) {
      console.error('❌ Error during seeding:', error);
      throw error;
    }
  }
}
