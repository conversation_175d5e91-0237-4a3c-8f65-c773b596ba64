import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SeederService } from './seeder.service';
import { PostalCodeSeederService } from "./postal-code.seeder";
import { LicenseSeederService } from './license.seeder.service';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { UserIdentification } from '../../entities/user-identification.entity';
import { IdentificationType } from '../../entities/identification-type.entity';
import { PostalCode } from "../../entities/postal-code.entity";
import { LicenseTypes } from '../../entities/license-types.entity';
import { LicenseCategories } from '../../entities/license-categories.entity';
import { LicenseCategoryDocument } from '../../entities/license-category-document.entity';
import { Organization } from 'src/entities/organization.entity';
import { Department } from 'src/entities/department.entity';
import { DepartmentSeederService } from './departments.seeder';
import { OrganizationSeederService } from './organizations.seeder';

@Module({
  imports: [TypeOrmModule.forFeature([
    User,
    Role,
    Permission,
    UserIdentification,
    IdentificationType,
    PostalCode,
    LicenseTypes,
    LicenseCategories,
    LicenseCategoryDocument,
    Organization,
    Department
  ])],
  providers: [SeederService, PostalCodeSeederService, LicenseSeederService, DepartmentSeederService, OrganizationSeederService],
  exports: [SeederService, PostalCodeSeederService, LicenseSeederService, DepartmentSeederService, OrganizationSeederService],
})
export class SeederModule {}
