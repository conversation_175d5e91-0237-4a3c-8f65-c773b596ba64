import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { ApplicationTaskHelperService } from '../applications/application-task-helper.service';
import {
  UpdateApplicationStatusDto,
  ApplicationStatusTrackingResponseDto,
  ApplicationStatusHistoryResponseDto
} from '../dto/application-status/update-application-status.dto';

@Injectable()
export class ApplicationStatusTrackingService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    @InjectRepository(ApplicationStatusHistory)
    private statusHistoryRepository: Repository<ApplicationStatusHistory>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private applicationTaskHelper: ApplicationTaskHelperService,
  ) {}

  async updateApplicationStatus(
    applicationId: string,
    updateStatusDto: UpdateApplicationStatusDto,
    userId: string
  ): Promise<ApplicationStatusTrackingResponseDto> {
    // Find the application
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations: ['applicant', 'license_category']
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    // Validate status transition
    this.validateStatusTransition(application.status, updateStatusDto.status);

    // Store previous status
    const previousStatus = application.status;

    // Update application status and progress
    const { newStep, newProgress } = this.calculateStepAndProgress(updateStatusDto.status);
    
    application.status = updateStatusDto.status;
    application.current_step = newStep;
    application.progress_percentage = newProgress;
    application.updated_by = userId;

    // Set submitted_at if status is being changed to submitted for the first time
    if (updateStatusDto.status === 'submitted' && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    // Save updated application
    await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    await this.applicationTaskHelper.handleApplicationSubmission(
      applicationId,
      previousStatus,
      updateStatusDto.status,
      userId
    );

    // Create status history record
    const statusHistory = new ApplicationStatusHistory();
    statusHistory.application_id = applicationId;
    statusHistory.status = updateStatusDto.status;
    statusHistory.previous_status = previousStatus;
    statusHistory.comments = updateStatusDto.comments;
    statusHistory.reason = updateStatusDto.reason;
    statusHistory.changed_by = updateStatusDto.changed_by || userId;
    
    if (updateStatusDto.estimated_completion_date) {
      statusHistory.estimated_completion_date = new Date(updateStatusDto.estimated_completion_date);
    }

    await this.statusHistoryRepository.save(statusHistory);

    // Return updated application with status history
    return this.getApplicationStatusTracking(applicationId);
  }

  async getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingResponseDto> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations: ['applicant', 'license_category']
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    // Get status history
    const statusHistory = await this.statusHistoryRepository.find({
      where: { application_id: applicationId },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    // Transform status history
    const transformedHistory: ApplicationStatusHistoryResponseDto[] = statusHistory.map(history => ({
      history_id: history.history_id,
      application_id: history.application_id,
      status: history.status,
      previous_status: history.previous_status,
      comments: history.comments,
      reason: history.reason,
      changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
      changed_at: history.changed_at,
      estimated_completion_date: history.estimated_completion_date
    }));

    return {
      application_id: application.application_id,
      application_number: application.application_number,
      current_status: application.status,
      current_step: application.current_step,
      progress_percentage: application.progress_percentage,
      submitted_at: application.submitted_at,
      created_at: application.created_at,
      updated_at: application.updated_at,
      status_history: transformedHistory,
      applicant: {
        name: application.applicant.name,
        email: application.applicant.email,
        business_registration_number: application.applicant.business_registration_number
      },
      license_category: {
        name: application.license_category.name,
        description: application.license_category.description
      }
    };
  }

  async getApplicationsByStatus(status: string): Promise<ApplicationStatusTrackingResponseDto[]> {
    const applications = await this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type'],
      order: { updated_at: 'DESC' }
    });

    if (applications.length === 0) {
      return [];
    }

    // Get all status histories for these applications in one query
    const applicationIds = applications.map(app => app.application_id);
    const statusHistories = await this.statusHistoryRepository.find({
      where: { application_id: In(applicationIds) },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    // Group status histories by application_id
    const historiesByAppId = statusHistories.reduce((acc, history) => {
      if (!acc[history.application_id]) {
        acc[history.application_id] = [];
      }
      acc[history.application_id].push(history);
      return acc;
    }, {} as Record<string, any[]>);

    // Transform to response DTOs
    return applications.map(application => {
      const appHistories = historiesByAppId[application.application_id] || [];
      const transformedHistory = appHistories.map(history => ({
        history_id: history.history_id,
        application_id: history.application_id,
        status: history.status,
        previous_status: history.previous_status,
        comments: history.comments,
        reason: history.reason,
        changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
        changed_at: history.changed_at,
        estimated_completion_date: history.estimated_completion_date
      }));

      return {
        application_id: application.application_id,
        application_number: application.application_number,
        current_status: application.status,
        current_step: application.current_step,
        progress_percentage: application.progress_percentage,
        submitted_at: application.submitted_at,
        created_at: application.created_at,
        updated_at: application.updated_at,
        status_history: transformedHistory,
        applicant: {
          name: application.applicant.name,
          email: application.applicant.email,
          business_registration_number: application.applicant.business_registration_number
        },
        license_category: {
          name: application.license_category.name,
          description: application.license_category.description
        }
      };
    });
  }

  private validateStatusTransition(currentStatus: string, newStatus: string): void {
    const validTransitions: Record<string, string[]> = {
      'draft': ['submitted'],
      'submitted': ['under_review'],
      'under_review': ['evaluation', 'rejected'],
      'evaluation': ['approved', 'rejected'],
      'approved': [], // Final status
      'rejected': [], // Final status
      'withdrawn': [] // Final status
    };

    if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}`
      );
    }
  }

  private calculateStepAndProgress(status: string): { newStep: number; newProgress: number } {
    const statusStepMap: Record<string, { step: number; progress: number }> = {
      'draft': { step: 1, progress: 14 },
      'submitted': { step: 2, progress: 25 },
      'under_review': { step: 3, progress: 50 },
      'evaluation': { step: 4, progress: 75 },
      'approved': { step: 5, progress: 100 },
      'rejected': { step: 1, progress: 0 },
      'withdrawn': { step: 1, progress: 0 }
    };

    const mapping = statusStepMap[status];
    if (!mapping) {
      throw new BadRequestException(`Unknown status: ${status}`);
    }

    return {
      newStep: mapping.step,
      newProgress: mapping.progress
    };
  }

  async getStatusHistory(applicationId: string): Promise<ApplicationStatusHistoryResponseDto[]> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId }
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    const statusHistory = await this.statusHistoryRepository.find({
      where: { application_id: applicationId },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    return statusHistory.map(history => ({
      history_id: history.history_id,
      application_id: history.application_id,
      status: history.status,
      previous_status: history.previous_status,
      comments: history.comments,
      reason: history.reason,
      changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
      changed_at: history.changed_at,
      estimated_completion_date: history.estimated_completion_date
    }));
  }
}
