import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from '../dto/role/create-role.dto';
import { UpdateRoleDto } from '../dto/role/update-role.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { Role } from '../entities';
import { PaginatedResult } from '../common/interfaces/pagination.interface';

@Controller('roles')
@UseGuards(JwtAuthGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<Role>> {
    return this.rolesService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.rolesService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ) {
    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.rolesService.remove(id);
    return { message: 'Role deleted successfully' };
  }

  @Post(':id/permissions')
  assignPermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('permission_ids') permissionIds: string[],
  ) {
    return this.rolesService.assignPermissions(id, permissionIds);
  }

  @Delete(':id/permissions')
  removePermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('permission_ids') permissionIds: string[],
  ) {
    return this.rolesService.removePermissions(id, permissionIds);
  }
}
