import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from '../entities/organization.entity';
import { UpdateOrganizationDto } from 'src/dto/organizations/update-organization.dto';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectRepository(Organization)
    private readonly orgRepo: Repository<Organization>,
  ) {}

  async create(dto: CreateOrganizationDto): Promise<Organization> {
    const organization = this.orgRepo.create(dto);
    return this.orgRepo.save(organization);
  }

  async findAll(): Promise<Organization[]> {
    return this.orgRepo.find({
      relations: ['physical_address', 'postal_address', 'contact', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Organization> {
    const organization = await this.orgRepo.findOne({
      where: { organization_id: id },
      relations: ['physical_address', 'postal_address', 'contact', 'creator', 'updater'],
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }
    return organization;
  }

  async update(id: string, dto: UpdateOrganizationDto): Promise<Organization> {
    const organization = await this.findOne(id);
    const updated = this.orgRepo.merge(organization, dto);
    return this.orgRepo.save(updated);
  }

  async remove(id: string): Promise<void> {
    const organization = await this.findOne(id);
    await this.orgRepo.softRemove(organization);
    this.logger.log(`Soft-deleted organization with ID ${id}`);
  }

  async hardDelete(id: string): Promise<void> {
    const organization = await this.findOne(id);
    await this.orgRepo.remove(organization);
    this.logger.warn(`Permanently deleted organization with ID ${id}`);
  }
}
