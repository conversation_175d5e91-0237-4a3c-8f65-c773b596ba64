import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not, LessThan } from 'typeorm';
import { Task, TaskStatus, TaskPriority } from '../entities/tasks.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { ApplicationsService } from '../applications/applications.service';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private readonly tasksRepository: Repository<Task>,
    @Inject(forwardRef(() => ApplicationsService))
    private readonly applicationsService: ApplicationsService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Task> = {
    sortableColumns: ['created_at', 'updated_at', 'task_number', 'title', 'status', 'priority'],
    searchableColumns: ['task_number', 'title', 'description'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 20,
    maxLimit: 100,
    relations: ['assignee', 'assigner', 'creator'],
    select: [
      'task_id',
      'task_number',
      'title',
      'description',
      'task_type',
      'status',
      'priority',
      'entity_type',
      'entity_id',
      'assigned_to',
      'assigned_by',
      'assigned_at',
      'due_date',
      'completed_at',
      'review',
      'review_notes',
      'completion_notes',
      'created_at',
      'created_by',
      'updated_at',
      'updated_by',
      'assignee.user_id',
      'assignee.first_name',
      'assignee.last_name',
      'assignee.email',
      'assigner.user_id',
      'assigner.first_name',
      'assigner.last_name',
      'assigner.email',
      'creator.user_id',
      'creator.first_name',
      'creator.last_name',
      'creator.email',
    ],
  };

  /**
   * Helper method to update application assignment when task is assigned
   */
  private async updateApplicationAssignment(task: Task, assignedTo: string, assignedBy: string): Promise<void> {
    // Only update application if this is an application-related task
    if (task.entity_type === 'application' && task.entity_id) {
      try {
        await this.applicationsService.assignApplication(task.entity_id, assignedTo, assignedBy);
        // Also update application status to evaluation if not already
        const application = await this.applicationsService.findOne(task.entity_id);
        if (application.status !== 'evaluation') {
          await this.applicationsService.updateStatus(task.entity_id, 'evaluation', assignedBy);
        }
      } catch (error) {
        console.warn(`Failed to update application assignment for task ${task.task_id}:`, error);
        // Don't fail the task assignment if application update fails
      }
    }
  }

  async create(createTaskDto: CreateTaskDto, creatorId: string): Promise<Task> {
    // Generate task number
    const taskCount = await this.tasksRepository.count();
    const taskNumber = `TASK-${String(taskCount + 1).padStart(6, '0')}`;

    const taskData: Partial<Task> = {
      task_type: createTaskDto.task_type,
      title: createTaskDto.title,
      description: createTaskDto.description,
      priority: createTaskDto.priority || TaskPriority.MEDIUM,
      status: createTaskDto.status || TaskStatus.PENDING,
      entity_type: createTaskDto.entity_type,
      entity_id: createTaskDto.entity_id,
      task_number: taskNumber,
      created_by: creatorId,
      assigned_by: creatorId, // Creator is also the assigner initially
      assigned_to: createTaskDto.assigned_to,
    };

    if (createTaskDto.due_date) {
      taskData.due_date = new Date(createTaskDto.due_date);
    }

    if (createTaskDto.assigned_to) {
      taskData.assigned_at = new Date();
    }

    const task = this.tasksRepository.create(taskData);
    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task and it's assigned
    if (createTaskDto.assigned_to) {
      await this.updateApplicationAssignment(savedTask, createTaskDto.assigned_to, creatorId);
    }

    return savedTask;
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Task>> {
    return paginate(query, this.tasksRepository, this.paginateConfig);
  }

  async findUnassigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: Not(IsNull()) },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssignedToUser(userId: string, query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findOne(id: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { task_id: id },
      relations: ['assignee', 'assigner', 'creator', 'updater'],
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${id} not found`);
    }

    return task;
  }

  /**
   * Get task with basic navigation information
   * Frontend will handle the actual navigation logic based on entity_type and entity_id
   */
  async findOneWithNavigationInfo(id: string): Promise<{
    task: Task;
    canNavigateToEntity: boolean;
  }> {
    const task = await this.findOne(id);

    // Just check if the task has entity information for navigation
    const canNavigateToEntity = !!(task.entity_type && task.entity_id);

    return {
      task,
      canNavigateToEntity,
    };
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task> {
    const task = await this.findOne(id);

    // Update completion timestamp if status is changed to completed
    if (updateTaskDto.status === 'completed' && task.status !== 'completed') {
      task.completed_at = new Date();
    }

    Object.assign(task, updateTaskDto);
    return this.tasksRepository.save(task);
  }

  async assign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    if (task.assigned_to) {
      throw new BadRequestException('Task is already assigned to another user. Use reassign instead.');
    }

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set assignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
      task.review_notes = notes;
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    return savedTask;
  }

  async reassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    const previousAssignee = task.assigned_to;

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set reassignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
      const reassignmentNote = `Reassigned from ${previousAssignee || 'unassigned'} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
      task.review_notes = reassignmentNote;
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    return savedTask;
  }

  /**
   * Unified method to assign or reassign a task
   * Automatically determines whether to assign or reassign based on current state
   */
  async assignOrReassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    const isReassignment = !!task.assigned_to;
    const previousAssignee = task.assigned_to;

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set assignment/reassignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;

      if (isReassignment) {
        const reassignmentNote = `Reassigned from ${previousAssignee} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
        task.review_notes = reassignmentNote;
      } else {
        task.review_notes = notes;
      }
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    return savedTask;
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id); // Verify task exists
    await this.tasksRepository.softDelete(id);
  }

  async getTaskStats(): Promise<{
    total: number;
    unassigned: number;
    assigned: number;
    completed: number;
    overdue: number;
  }> {
    const [total, unassigned, assigned, completed, overdue] = await Promise.all([
      this.tasksRepository.count(),
      this.tasksRepository.count({ where: { assigned_to: IsNull() } }),
      this.tasksRepository.count({ where: { assigned_to: Not(IsNull()) } }),
      this.tasksRepository.count({ where: { status: TaskStatus.COMPLETED } }),
      this.tasksRepository.count({
        where: {
          due_date: Not(IsNull()),
          status: Not(TaskStatus.COMPLETED),
        },
      }),
    ]);

    return {
      total,
      unassigned,
      assigned,
      completed,
      overdue,
    };
  }
}