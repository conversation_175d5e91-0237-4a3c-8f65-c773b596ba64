import { Module } from '@nestjs/common';
import { StandardsController } from './standards.controller';
import { StandardsService } from './standards.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeApprovedDevice } from 'src/entities/type_approved_device.entity';
import { TypeApprovedManufacturer } from 'src/entities/type_approved_manufacturer.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TypeApprovedDevice, TypeApprovedManufacturer])],
  controllers: [StandardsController],
  providers: [StandardsService],
  exports: [StandardsService],
})
export class StandardsModule {}
