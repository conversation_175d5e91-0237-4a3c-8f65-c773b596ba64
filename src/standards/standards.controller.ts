import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { StandardsService } from './standards.service';
import { CreateTypeApprovedManufacturerDto } from 'src/dto/type_approval/create.dto';
import { UpdateTypeApprovedManufacturerDto } from 'src/dto/type_approval/update.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { Audit } from 'src/common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from 'src/entities/audit-trail.entity';

@ApiTags('Standards - Type Approval and Shortcodes')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller('standards')
export class StandardsController {
  constructor(private readonly standardsService: StandardsService) {}

  @Post('manufacturers')
  @ApiOperation({ summary: 'Create Type Approved Manufacturer' })
  @ApiResponse({ status: 201, description: 'Manufacturer created successfully' })
  @ApiBody({ type: CreateTypeApprovedManufacturerDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'TypeApprovedManufacturer',
    description: 'Created a type approved manufacturer',
  })
  async createManufacturer(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    dto: CreateTypeApprovedManufacturerDto,
  ) {
    return this.standardsService.createManufacturer(dto);
  }

  @Get('manufacturers')
  @ApiOperation({ summary: 'List all Type Approved Manufacturers' })
  @ApiResponse({ status: 200, description: 'List of manufacturers' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'TypeApprovedManufacturer',
    description: 'Viewed all manufacturers',
  })
  async findAllManufacturers() {
    return this.standardsService.findAllManufacturers();
  }

  @Get('manufacturers/:id')
  @ApiOperation({ summary: 'Get a Type Approved Manufacturer by ID' })
  @ApiResponse({ status: 404, description: 'Manufacturer not found' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'TypeApprovedManufacturer',
    description: 'Viewed a manufacturer by ID',
  })
  async findOneManufacturer(@Param('id') id: string) {
    return this.standardsService.findOneManufacturer(id);
  }

  @Put('manufacturers/:id')
  @ApiOperation({ summary: 'Update Type Approved Manufacturer' })
  @ApiResponse({ status: 200, description: 'Manufacturer updated successfully' })
  @ApiBody({ type: UpdateTypeApprovedManufacturerDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'TypeApprovedManufacturer',
    description: 'Updated a manufacturer',
  })
  async updateManufacturer(
    @Param('id') id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    dto: UpdateTypeApprovedManufacturerDto,
  ) {
    return this.standardsService.updateManufacturer(id, dto);
  }

  @Delete('manufacturers/:id')
  @ApiOperation({ summary: 'Soft delete a Type Approved Manufacturer' })
  @ApiResponse({ status: 200, description: 'Manufacturer soft deleted successfully' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TYPE_APPROVAL_SERVICES,
    resourceType: 'TypeApprovedManufacturer',
    description: 'Deleted a manufacturer',
  })
  async removeManufacturer(@Param('id') id: string) {
    await this.standardsService.removeManufacturer(id);
    return { message: 'Manufacturer soft deleted successfully' };
  }
}
