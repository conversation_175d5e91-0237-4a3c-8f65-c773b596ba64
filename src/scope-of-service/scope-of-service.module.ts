import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScopeOfServiceController } from './scope-of-service.controller';
import { ScopeOfServiceService } from './scope-of-service.service';
import { ScopeOfService } from '../entities/scope-of-service.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ScopeOfService])],
  controllers: [ScopeOfServiceController],
  providers: [ScopeOfServiceService],
  exports: [ScopeOfServiceService],
})
export class ScopeOfServiceModule {}
