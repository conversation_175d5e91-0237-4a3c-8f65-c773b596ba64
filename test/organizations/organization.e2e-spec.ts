/**
 * Organization E2E Test with <PERSON>W<PERSON> Auth and Seeded Data
 *
 * This test file verifies the full lifecycle of the Organization module:
 * - Authenticates with a test user and retrieves a JWT token
 * - Seeds an initial organization
 * - Tests CRUD endpoints under /organizations with proper authorization
 *
 * Commands to run this test:
 *   $ npm run test:e2e -- Organization
 */

import request from 'supertest';
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { AppModule } from 'src/app.module';
import { DataSource } from 'typeorm';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';
import { Organization } from 'src/entities/organization.entity';

describe('Organization (e2e)', () => {
  jest.setTimeout(100000);
  let app: INestApplication;
  let httpServer: any;
  let accessToken: string;
  let seededOrg: Organization;
  let dataSource: DataSource;

  const orgDto: CreateOrganizationDto = {
    name: 'E2E Org',
    registration_number: 'E2E-REG-123',
    website: 'https://e2etest.org',
    email: '<EMAIL>',
    phone: '+265987654321',
    fax: undefined,
    physical_address_id: undefined,
    postal_address_id: undefined,
    contact_id: undefined,
    date_incorporation: new Date('2023-01-01'),
    place_incorporation: 'E2E City',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }));
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    // Simulate login and extract JWT token
    const loginRes = await request(httpServer).post('/auth/login').send({
      username: '<EMAIL>', // Make sure this user exists in DB
      password: 'Admin123!',
    });
    accessToken = loginRes.body.access_token;

    // Seed organization
    const orgRepo = dataSource.getRepository(Organization);
    seededOrg = orgRepo.create({
      ...orgDto,
      organization_id: 'org-e2e-1234',
    });
    await orgRepo.save(seededOrg);
  });

  afterAll(async () => {
    await dataSource.getRepository(Organization).delete({ organization_id: 'org-e2e-1234' });
    await app.close();
  });

  describe('/GET organizations', () => {
    it('should return list of organizations', async () => {
      const res = await request(httpServer)
        .get('/organizations')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(Array.isArray(res.body)).toBe(true);
    });
  });

  describe('/GET organizations/:id', () => {
    it('should return organization by id', async () => {
      const res = await request(httpServer)
        .get(`/organizations/${seededOrg.organization_id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(res.body.name).toBe(orgDto.name);
    });
  });

  describe('/POST organizations', () => {
    it('should create a new organization', async () => {
      const newOrgDto = {
        ...orgDto,
        registration_number: 'E2E-REG-999',
        name: 'New E2E Org',
      };

      const res = await request(httpServer)
        .post('/organizations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(newOrgDto)
        .expect(201);

      expect(res.body.name).toBe('New E2E Org');

      // Clean up created org
      await dataSource.getRepository(Organization).delete({ organization_id: res.body.organization_id });
    });
  });

  describe('/PUT organizations/:id', () => {
    it('should update an existing organization', async () => {
      const updateDto = { ...orgDto, name: 'Updated E2E Org' };

      const res = await request(httpServer)
        .put(`/organizations/${seededOrg.organization_id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateDto)
        .expect(200);

      expect(res.body.name).toBe('Updated E2E Org');
    });
  });

  describe('/DELETE organizations/:id', () => {
    it('should soft delete the organization', async () => {
      const uniqueSuffix = Math.floor(Math.random() * 100000);
      const tempOrg = await dataSource.getRepository(Organization).save({
        ...orgDto,
        organization_id: `temp-e2e-org-${uniqueSuffix}`,
        name: `To Be Deleted ${uniqueSuffix}`,
        registration_number: `TEMP-E2E-DELETE-${uniqueSuffix}`,
      });

      await request(httpServer)
        .delete(`/organizations/${tempOrg.organization_id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(204);

      const deleted = await dataSource.getRepository(Organization).findOneBy({ organization_id: tempOrg.organization_id });
      expect(deleted?.deleted_at).toBeDefined();
    });
  });
});
